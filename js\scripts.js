// Array con colores para los encabezados de tarjetas
const cardColors = [
    '#008080', // Teal
    '#007575', // Teal oscuro
    '#009090'  // Teal claro
];

// Función para inicializar la página
function initPage() {
    setupCardColors();
    setupCardEvents();
    setupSearch();
}

// Asignar colores aleatorios a los encabezados de las tarjetas
function setupCardColors() {
    document.querySelectorAll('.form-card-header').forEach(header => {
        const randomColorIndex = Math.floor(Math.random() * cardColors.length);
        header.style.backgroundColor = cardColors[randomColorIndex];
    });
}

// Configurar eventos para las tarjetas

// Configurar búsqueda de formularios
function setupSearch() {
    const searchInput = document.getElementById('search-forms');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            
            document.querySelectorAll('.form-card').forEach(card => {
                const title = card.querySelector('.form-title').textContent.toLowerCase();
                const subtitle = card.querySelector('.form-subtitle').textContent.toLowerCase();
                
                if (title.includes(searchTerm) || subtitle.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    }
}

// ====================================================
// MANEJO DE PESTAÑAS PARA FORMULARIOS ENVIADOS
// ====================================================

// Función para manejar el cambio de pestañas
function handleTabSwitch(sectionName) {
    // Actualizar estado de botones de pestañas
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.section === sectionName) {
            btn.classList.add('active');
        }
    });

    // Mostrar/ocultar secciones
    const availableSection = document.getElementById('available-forms');
    const submittedSection = document.getElementById('submitted-forms');

    if (sectionName === 'available') {
        availableSection.classList.remove('hidden');
        submittedSection.classList.add('hidden');
    } else if (sectionName === 'submitted') {
        availableSection.classList.add('hidden');
        submittedSection.classList.remove('hidden');
        
        // Notificar al gestor de formularios enviados
        if (window.submittedFormsManager) {
            window.submittedFormsManager.switchSection('submitted');
        }
    }
}

// Configurar eventos de pestañas
function setupTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const section = e.currentTarget.dataset.section;
            handleTabSwitch(section);
        });
    });
}

// Agregar configuración de pestañas a la inicialización
function initPage() {
    setupCardColors();
    setupCardEvents();
    setupSearch();
    setupTabs(); // Agregar configuración de pestañas
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', initPage); 