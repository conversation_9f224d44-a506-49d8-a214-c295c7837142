/**
 * ====================================================
 * SYSTEM DIAGNOSTIC
 * Herramienta de diagnóstico completo para el sistema VIVA
 * ====================================================
 */

class SystemDiagnostic {
    constructor() {
        this.results = {};
        this.startTime = null;
        this.endTime = null;
    }

    /**
     * Ejecutar diagnóstico completo del sistema
     * @returns {Object} - Resultados del diagnóstico
     */
    async runFullDiagnostic() {
        this.startTime = new Date();
        console.group('🔍 DIAGNÓSTICO COMPLETO DEL SISTEMA VIVA');
        
        try {
            // 1. Verificar configuración AWS
            console.log('1️⃣ Verificando configuración AWS...');
            this.results.aws = await this.diagnoseAWS();
            
            // 2. Verificar sistema de imágenes
            console.log('2️⃣ Verificando sistema de imágenes...');
            this.results.images = await this.diagnoseImages();
            
            // 3. Verificar localStorage
            console.log('3️⃣ Verificando localStorage...');
            this.results.localStorage = await this.diagnoseLocalStorage();
            
            // 4. Verificar conectividad
            console.log('4️⃣ Verificando conectividad...');
            this.results.connectivity = await this.diagnoseConnectivity();
            
            // 5. Verificar formulario
            console.log('5️⃣ Verificando formulario...');
            this.results.form = await this.diagnoseForm();
            
            this.endTime = new Date();
            this.results.duration = this.endTime - this.startTime;
            
            // Mostrar resumen
            this.showDiagnosticSummary();
            
            return this.results;
            
        } catch (error) {
            console.error('❌ Error en diagnóstico:', error);
            this.results.error = error.message;
            return this.results;
        } finally {
            console.groupEnd();
        }
    }

    /**
     * Diagnosticar configuración AWS
     * @returns {Object} - Resultados AWS
     */
    async diagnoseAWS() {
        const aws = {
            sdkLoaded: !!window.AWS,
            configLoaded: !!window.AWS_CONFIG,
            development: false,
            region: null,
            bucket: null,
            connection: false,
            errorHandler: !!window.awsErrorHandler,
            issues: []
        };

        try {
            if (window.AWS_CONFIG) {
                aws.development = window.AWS_CONFIG.development;
                aws.region = window.AWS_CONFIG.region;
                aws.bucket = window.AWS_CONFIG.bucket;
            } else {
                aws.issues.push('AWS_CONFIG no encontrado');
            }

            if (window.AWS) {
                // Probar conexión si no está en modo desarrollo
                if (!aws.development) {
                    try {
                        if (window.testAWSConnection) {
                            aws.connection = await window.testAWSConnection();
                        }
                    } catch (error) {
                        aws.issues.push(`Error probando conexión: ${error.message}`);
                    }
                } else {
                    aws.connection = 'mock'; // Conexión simulada en desarrollo
                }
            } else {
                aws.issues.push('AWS SDK no cargado');
            }

            console.log(`✅ AWS SDK: ${aws.sdkLoaded ? 'Cargado' : 'No cargado'}`);
            console.log(`⚙️ Modo: ${aws.development ? 'Desarrollo (Mock)' : 'Producción'}`);
            console.log(`🌍 Región: ${aws.region || 'No configurada'}`);
            console.log(`🪣 Bucket: ${aws.bucket || 'No configurado'}`);
            console.log(`🔗 Conexión: ${aws.connection === true ? 'OK' : aws.connection === 'mock' ? 'Mock' : 'Error'}`);

        } catch (error) {
            aws.issues.push(`Error general: ${error.message}`);
        }

        return aws;
    }

    /**
     * Diagnosticar sistema de imágenes
     * @returns {Object} - Resultados de imágenes
     */
    async diagnoseImages() {
        const images = {
            managerLoaded: !!window.imageManager,
            uploaderLoaded: !!window.s3ImageUploader,
            errorHandlerLoaded: !!window.awsErrorHandler,
            localImages: 0,
            s3Images: 0,
            fallbackImages: 0,
            totalSize: 0,
            issues: []
        };

        try {
            // Verificar ImageManager
            if (window.imageManager) {
                const stats = window.imageManager.getStats();
                console.log('📊 ImageManager stats:', stats);
            } else {
                images.issues.push('ImageManager no cargado');
            }

            // Verificar imágenes en localStorage
            const urlParams = new URLSearchParams(window.location.search);
            const mode = urlParams.get('edit') ? 'edit' : 'draft';
            const formId = urlParams.get('edit') || null;

            if (window.localStorageManager) {
                const savedImages = window.localStorageManager.loadImages(mode, formId);
                images.localImages = savedImages.length;

                savedImages.forEach(img => {
                    if (img.s3_url && !img.fallback) {
                        images.s3Images++;
                    } else if (img.fallback) {
                        images.fallbackImages++;
                    }
                    images.totalSize += img.size || 0;
                });

                console.log(`📸 Imágenes locales: ${images.localImages}`);
                console.log(`☁️ Imágenes en S3: ${images.s3Images}`);
                console.log(`📦 Imágenes fallback: ${images.fallbackImages}`);
                console.log(`💾 Tamaño total: ${(images.totalSize / (1024 * 1024)).toFixed(2)} MB`);
            } else {
                images.issues.push('LocalStorageManager no cargado');
            }

            // Verificar error handler
            if (window.awsErrorHandler) {
                const errorStats = window.awsErrorHandler.getErrorStats();
                images.errorStats = errorStats;
                console.log('🛡️ Error handler stats:', errorStats);
            }

        } catch (error) {
            images.issues.push(`Error verificando imágenes: ${error.message}`);
        }

        return images;
    }

    /**
     * Diagnosticar localStorage
     * @returns {Object} - Resultados localStorage
     */
    async diagnoseLocalStorage() {
        const storage = {
            available: false,
            quota: 0,
            used: 0,
            remaining: 0,
            vivaFiles: 0,
            issues: []
        };

        try {
            // Verificar disponibilidad
            storage.available = typeof(Storage) !== "undefined";

            if (storage.available) {
                // Estimar cuota (no hay API estándar)
                try {
                    const estimate = await navigator.storage.estimate();
                    storage.quota = estimate.quota || 0;
                    storage.used = estimate.usage || 0;
                    storage.remaining = storage.quota - storage.used;
                } catch (error) {
                    // Fallback para navegadores sin storage.estimate
                    storage.quota = 10 * 1024 * 1024; // Estimación de 10MB
                }

                // Contar archivos VIVA
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key.startsWith('viva_')) {
                        storage.vivaFiles++;
                    }
                }

                console.log(`💾 LocalStorage disponible: ${storage.available}`);
                console.log(`📊 Cuota: ${(storage.quota / (1024 * 1024)).toFixed(2)} MB`);
                console.log(`📈 Usado: ${(storage.used / (1024 * 1024)).toFixed(2)} MB`);
                console.log(`📁 Archivos VIVA: ${storage.vivaFiles}`);

            } else {
                storage.issues.push('LocalStorage no disponible');
            }

        } catch (error) {
            storage.issues.push(`Error verificando localStorage: ${error.message}`);
        }

        return storage;
    }

    /**
     * Diagnosticar conectividad
     * @returns {Object} - Resultados conectividad
     */
    async diagnoseConnectivity() {
        const connectivity = {
            online: navigator.onLine,
            s3Reachable: false,
            apiReachable: false,
            latency: null,
            issues: []
        };

        try {
            // Probar conectividad a S3
            if (window.AWS_CONFIG && !window.AWS_CONFIG.development) {
                try {
                    const s3Url = `https://${window.AWS_CONFIG.bucket}.s3.amazonaws.com/`;
                    const startTime = Date.now();
                    const response = await fetch(s3Url, { method: 'HEAD', mode: 'no-cors' });
                    connectivity.latency = Date.now() - startTime;
                    connectivity.s3Reachable = true;
                } catch (error) {
                    connectivity.issues.push(`S3 no alcanzable: ${error.message}`);
                }
            }

            // Probar API local (si existe)
            try {
                const response = await fetch('/api/health', { method: 'GET' });
                connectivity.apiReachable = response.ok;
            } catch (error) {
                connectivity.issues.push(`API local no disponible: ${error.message}`);
            }

            console.log(`🌐 Online: ${connectivity.online}`);
            console.log(`☁️ S3 alcanzable: ${connectivity.s3Reachable}`);
            console.log(`🔌 API alcanzable: ${connectivity.apiReachable}`);
            console.log(`⚡ Latencia: ${connectivity.latency || 'N/A'} ms`);

        } catch (error) {
            connectivity.issues.push(`Error verificando conectividad: ${error.message}`);
        }

        return connectivity;
    }

    /**
     * Diagnosticar formulario
     * @returns {Object} - Resultados formulario
     */
    async diagnoseForm() {
        const form = {
            handlerLoaded: !!window.handler,
            formExists: !!document.querySelector('form'),
            fieldsCount: 0,
            filledFields: 0,
            imageInputs: 0,
            issues: []
        };

        try {
            const formElement = document.querySelector('form');
            if (formElement) {
                // Contar campos
                const inputs = formElement.querySelectorAll('input, select, textarea');
                form.fieldsCount = inputs.length;

                // Contar campos llenos
                inputs.forEach(input => {
                    if (input.value && input.value.trim() !== '') {
                        form.filledFields++;
                    }
                });

                // Contar inputs de imagen
                const imageInputs = formElement.querySelectorAll('input[type="file"]');
                form.imageInputs = imageInputs.length;

                console.log(`📝 Formulario encontrado: ${form.formExists}`);
                console.log(`📊 Campos totales: ${form.fieldsCount}`);
                console.log(`✅ Campos llenos: ${form.filledFields}`);
                console.log(`📸 Inputs de imagen: ${form.imageInputs}`);

            } else {
                form.issues.push('Formulario no encontrado');
            }

            if (!window.handler) {
                form.issues.push('Handler del formulario no cargado');
            }

        } catch (error) {
            form.issues.push(`Error verificando formulario: ${error.message}`);
        }

        return form;
    }

    /**
     * Mostrar resumen del diagnóstico
     */
    showDiagnosticSummary() {
        console.group('📋 RESUMEN DEL DIAGNÓSTICO');
        
        const totalIssues = Object.values(this.results)
            .reduce((total, section) => total + (section.issues?.length || 0), 0);

        console.log(`⏱️ Duración: ${this.results.duration} ms`);
        console.log(`⚠️ Problemas encontrados: ${totalIssues}`);

        if (totalIssues === 0) {
            console.log('✅ Sistema funcionando correctamente');
        } else {
            console.log('❌ Se encontraron problemas:');
            
            Object.entries(this.results).forEach(([section, data]) => {
                if (data.issues && data.issues.length > 0) {
                    console.group(`🔸 ${section.toUpperCase()}`);
                    data.issues.forEach(issue => console.log(`  • ${issue}`));
                    console.groupEnd();
                }
            });
        }

        console.groupEnd();
    }

    /**
     * Generar reporte en formato JSON
     * @returns {string} - Reporte JSON
     */
    generateReport() {
        return JSON.stringify({
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            results: this.results
        }, null, 2);
    }

    /**
     * Descargar reporte como archivo
     */
    downloadReport() {
        const report = this.generateReport();
        const blob = new Blob([report], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `viva-diagnostic-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        console.log('📄 Reporte descargado');
    }
}

// Crear instancia global
const systemDiagnostic = new SystemDiagnostic();

// Hacer disponible globalmente
window.systemDiagnostic = systemDiagnostic;

// Funciones de conveniencia
window.runDiagnostic = () => systemDiagnostic.runFullDiagnostic();
window.downloadDiagnosticReport = () => systemDiagnostic.downloadReport();

console.log('✅ SystemDiagnostic inicializado');
console.log('💡 Funciones disponibles:');
console.log('   - runDiagnostic() - Ejecutar diagnóstico completo');
console.log('   - downloadDiagnosticReport() - Descargar reporte');
