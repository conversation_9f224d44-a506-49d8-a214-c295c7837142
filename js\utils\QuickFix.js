/**
 * ====================================================
 * QUICK FIX UTILITIES
 * Herramientas rápidas para solucionar problemas comunes
 * ====================================================
 */

window.QuickFix = {
    
    /**
     * Verificar y corregir configuración AWS
     */
    fixAWSConfig() {
        console.group('🔧 QUICK FIX: Configuración AWS');
        
        try {
            // Verificar si AWS_CONFIG existe
            if (typeof AWS_CONFIG === 'undefined') {
                console.error('❌ AWS_CONFIG no encontrado');
                
                // Crear configuración temporal
                window.AWS_CONFIG = {
                    development: false,
                    region: 'us-east-2',
                    bucket: 'viva-inspectores-storage',
                    accessKeyId: 'AKIAIOSFODNN7EXAMPLE',
                    secretAccessKey: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                    enableLocalFallback: true
                };
                
                console.log('✅ AWS_CONFIG creado temporalmente');
            } else {
                console.log('✅ AWS_CONFIG encontrado');
            }
            
            // Verificar configuración
            const config = window.AWS_CONFIG || AWS_CONFIG;
            console.log('📊 Configuración actual:', {
                development: config.development,
                region: config.region,
                bucket: config.bucket,
                hasCredentials: !!(config.accessKeyId && config.secretAccessKey)
            });
            
            // Verificar AWS SDK
            if (window.AWS) {
                console.log('✅ AWS SDK cargado');
                
                // Configurar AWS si no está en modo desarrollo
                if (!config.development) {
                    try {
                        window.AWS.config.update({
                            accessKeyId: config.accessKeyId,
                            secretAccessKey: config.secretAccessKey,
                            region: config.region
                        });
                        console.log('✅ AWS SDK configurado');
                    } catch (error) {
                        console.error('❌ Error configurando AWS SDK:', error);
                    }
                }
            } else {
                console.error('❌ AWS SDK no cargado');
            }
            
        } catch (error) {
            console.error('❌ Error en fixAWSConfig:', error);
        } finally {
            console.groupEnd();
        }
    },
    
    /**
     * Verificar y corregir LocalStorageManager
     */
    fixLocalStorageManager() {
        console.group('🔧 QUICK FIX: LocalStorageManager');
        
        try {
            if (!window.localStorageManager) {
                console.error('❌ LocalStorageManager no encontrado');
                console.log('💡 Intentando recargar...');
                
                // Intentar crear instancia manual
                if (typeof LocalStorageManager !== 'undefined') {
                    window.localStorageManager = new LocalStorageManager();
                    console.log('✅ LocalStorageManager creado manualmente');
                } else {
                    console.error('❌ Clase LocalStorageManager no disponible');
                }
            } else {
                console.log('✅ LocalStorageManager encontrado');
                
                // Verificar métodos
                const methods = ['loadImages', 'saveImages', 'getImages', 'setImages'];
                methods.forEach(method => {
                    if (typeof window.localStorageManager[method] === 'function') {
                        console.log(`✅ Método ${method} disponible`);
                    } else {
                        console.error(`❌ Método ${method} NO disponible`);
                    }
                });
            }
            
        } catch (error) {
            console.error('❌ Error en fixLocalStorageManager:', error);
        } finally {
            console.groupEnd();
        }
    },
    
    /**
     * Verificar y corregir ImageManager
     */
    fixImageManager() {
        console.group('🔧 QUICK FIX: ImageManager');
        
        try {
            if (!window.imageManager) {
                console.error('❌ ImageManager no encontrado');
                
                if (typeof ImageManager !== 'undefined') {
                    window.imageManager = new ImageManager();
                    console.log('✅ ImageManager creado manualmente');
                } else {
                    console.error('❌ Clase ImageManager no disponible');
                }
            } else {
                console.log('✅ ImageManager encontrado');
                
                // Verificar configuración
                const stats = window.imageManager.getStats();
                console.log('📊 ImageManager stats:', stats);
            }
            
        } catch (error) {
            console.error('❌ Error en fixImageManager:', error);
        } finally {
            console.groupEnd();
        }
    },
    
    /**
     * Ejecutar todas las correcciones
     */
    fixAll() {
        console.log('🚀 EJECUTANDO TODAS LAS CORRECCIONES...');
        
        this.fixAWSConfig();
        this.fixLocalStorageManager();
        this.fixImageManager();
        
        // Esperar un poco y ejecutar diagnóstico
        setTimeout(() => {
            if (window.runDiagnostic) {
                console.log('🔍 Ejecutando diagnóstico después de correcciones...');
                window.runDiagnostic();
            }
        }, 1000);
    },
    
    /**
     * Reinicializar todo el sistema
     */
    reinitialize() {
        console.log('🔄 REINICIALIZANDO SISTEMA...');
        
        try {
            // Limpiar variables globales
            delete window.AWS_CONFIG;
            delete window.localStorageManager;
            delete window.imageManager;
            delete window.systemDiagnostic;
            delete window.awsErrorHandler;
            
            console.log('🧹 Variables globales limpiadas');
            
            // Recargar página
            setTimeout(() => {
                console.log('🔄 Recargando página...');
                window.location.reload();
            }, 1000);
            
        } catch (error) {
            console.error('❌ Error reinicializando:', error);
        }
    },
    
    /**
     * Probar subida de imagen de prueba
     */
    async testImageUpload() {
        console.group('🧪 PRUEBA DE SUBIDA DE IMAGEN');
        
        try {
            // Crear imagen de prueba
            const canvas = document.createElement('canvas');
            canvas.width = 100;
            canvas.height = 100;
            const ctx = canvas.getContext('2d');
            
            // Dibujar algo simple
            ctx.fillStyle = '#ff0000';
            ctx.fillRect(0, 0, 100, 100);
            ctx.fillStyle = '#ffffff';
            ctx.font = '20px Arial';
            ctx.fillText('TEST', 25, 55);
            
            // Convertir a blob
            const blob = await new Promise(resolve => {
                canvas.toBlob(resolve, 'image/png');
            });
            
            // Crear archivo de prueba
            const testFile = new File([blob], 'test-image.png', { type: 'image/png' });
            
            console.log('📸 Imagen de prueba creada:', testFile.name, testFile.size, 'bytes');
            
            // Intentar procesar con ImageManager
            if (window.imageManager) {
                const result = await window.imageManager.processFiles([testFile], 'draft', null, 'test_section');
                console.log('✅ Procesamiento exitoso:', result);
            } else {
                console.error('❌ ImageManager no disponible');
            }
            
        } catch (error) {
            console.error('❌ Error en prueba de imagen:', error);
        } finally {
            console.groupEnd();
        }
    },
    
    /**
     * Mostrar información del sistema
     */
    showSystemInfo() {
        console.group('ℹ️ INFORMACIÓN DEL SISTEMA');
        
        console.log('🌐 Navegador:', navigator.userAgent);
        console.log('📍 URL:', window.location.href);
        console.log('💾 LocalStorage disponible:', typeof(Storage) !== "undefined");
        console.log('🔧 AWS SDK:', !!window.AWS);
        console.log('⚙️ AWS_CONFIG:', typeof AWS_CONFIG !== 'undefined' || !!window.AWS_CONFIG);
        console.log('🗄️ LocalStorageManager:', !!window.localStorageManager);
        console.log('🖼️ ImageManager:', !!window.imageManager);
        console.log('🔍 SystemDiagnostic:', !!window.systemDiagnostic);
        console.log('🛡️ AWSErrorHandler:', !!window.awsErrorHandler);
        
        // Mostrar errores de consola recientes
        console.log('📋 Para ver errores detallados, ejecuta: QuickFix.fixAll()');
        
        console.groupEnd();
    }
};

// Hacer disponible globalmente
window.QuickFix = QuickFix;

console.log('🔧 QuickFix cargado');
console.log('💡 Comandos disponibles:');
console.log('   - QuickFix.fixAll() - Corregir todos los problemas');
console.log('   - QuickFix.fixAWSConfig() - Corregir AWS');
console.log('   - QuickFix.fixLocalStorageManager() - Corregir LocalStorage');
console.log('   - QuickFix.fixImageManager() - Corregir ImageManager');
console.log('   - QuickFix.testImageUpload() - Probar subida de imagen');
console.log('   - QuickFix.showSystemInfo() - Mostrar info del sistema');
console.log('   - QuickFix.reinitialize() - Reinicializar todo');
