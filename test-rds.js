require('dotenv').config();
const mysql = require('mysql2/promise');

async function testRDS() {
    try {
        console.log('🔍 Probando conexión RDS...');
        
        const config = {
            host: process.env.DB_HOST,
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            database: process.env.DB_NAME,
            port: process.env.DB_PORT || 3306,
            connectTimeout: 10000
        };
        
        console.log('📋 Config:', { ...config, password: '***' });
        
        const connection = await mysql.createConnection(config);
        console.log('✅ CONEXIÓN EXITOSA');
        
        // Probar query simple
        const [rows] = await connection.execute('SELECT 1 as test');
        console.log('✅ QUERY EXITOSA:', rows);
        
        await connection.end();
        console.log('✅ CONEXIÓN CERRADA');
        
    } catch (error) {
        console.log('❌ ERROR:', error.code, error.message);
    }
}

testRDS();