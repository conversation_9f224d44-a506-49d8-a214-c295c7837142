# Diagrama de Componentes - Sistema VIVA_INSPECTORES

## Versión Mejorada: Arquitectura por Capas

```mermaid
flowchart TB
    subgraph "Capa de Presentación"
        A[Usuario] --> B[Navegador Web]
        B --> C[index.html]
        C --> D[Formularios HTML]
    end
    
    subgraph "Capa de Lógica de Negocio"
        D --> E[Form Handlers]
        E --> F[Validadores]
        E --> G[Document Generators]
        E --> K[PDF Manager]
    end
    
    subgraph "Capa de Procesamiento"
        G --> H[Plantillas de Documentos]
        G --> I[Procesador de Plantillas]
        I --> J[Documento Final]
        K --> L[html2pdf.js]
        K --> M[Previsualización PDF]
    end
    
    subgraph "Capa de Servidor"
        N[Servidor Express] --> O[Multer Upload]
        N --> P[Rutas API]
        P --> G
    end
    
    subgraph "Capa de Almacenamiento"
        Q[File System] --> H
        Q --> R[uploads/]
        O --> R
    end
    
    style A fill:#e1f5fe
    style J fill:#c8e6c9
    style N fill:#fff3e0
```

## Descripción
Este diagrama muestra la arquitectura general del sistema organizada por capas, con diseño limpio y colores que facilitan la comprensión de cada componente y su función.

---

## Versiones Alternativas

### Opción 2: Flujo Horizontal

```mermaid
flowchart LR
    subgraph "Frontend"
        A[Usuario] --> B[Navegador]
        B --> C[Formularios]
    end
    
    subgraph "Procesamiento"
        C --> D[Form Handler]
        D --> E[Validador]
        D --> F[Generador]
        D --> G[PDF Manager]
    end
    
    subgraph "Backend"
        H[Express Server]
        I[File Upload]
        J[API Routes]
    end
    
    subgraph "Storage"
        K[Plantillas]
        L[Uploads]
        M[File System]
    end
    
    F --> K
    F --> N[Documento Final]
    G --> O[Preview PDF]
    
    J --> F
    I --> L
    M --> K
    M --> L
    
    N --> A
    O --> A
    
    style A fill:#e3f2fd
    style N fill:#e8f5e8
    style H fill:#fff8e1
```

### Opción 3: Flujo del Usuario (Orientado a Proceso)

```mermaid
graph TD
    A[Usuario] --> B{Seleccionar<br/>Formulario}
    B --> C[Llenar Datos]
    C --> D[Agregar Evidencia]
    D --> E{Validar<br/>Información}
    
    E -->|Error| C
    E -->|Válido| F[Procesar Formulario]
    
    F --> G[Cargar Plantilla]
    F --> H[Procesar Imágenes]
    
    G --> I[Generar Documento]
    H --> I
    
    I --> J{Tipo de<br/>Salida}
    J -->|PDF| K[Generar PDF]
    J -->|Documento| L[Generar Doc]
    
    K --> M[Previsualizar]
    L --> M
    
    M --> N{¿Correcto?}
    N -->|No| C
    N -->|Sí| O[Descargar]
    
    O --> P[Completado]
    
    subgraph "Sistema Backend"
        Q[Express Server]
        R[File System]
        S[Storage]
    end
    
    F -.-> Q
    G -.-> R
    H -.-> S
    
    style A fill:#e3f2fd
    style P fill:#e8f5e8
    style Q fill:#fff8e1
```

## Mejoras Implementadas
- **Eliminé el nodo "Untitled Node"** que no tenía propósito
- **Organización por capas** para mejor comprensión arquitectónica
- **Diseño limpio y profesional** sin elementos visuales distractores
- **Colores diferenciados** por tipo de elemento
- **Flujo más claro** sin líneas confusas
- **Subgráficos** para agrupar componentes relacionados 