require('dotenv').config();
const mysql = require('mysql2/promise');

async function createTables() {
    let connection;
    try {
        const config = {
            host: process.env.DB_HOST,
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            database: process.env.DB_NAME,
            port: parseInt(process.env.DB_PORT) || 3306
        };
        
        connection = await mysql.createConnection(config);
        console.log('✅ Conectado a RDS MySQL');
        
        // Crear tabla form_submissions
        const createTableSQL = `
            CREATE TABLE IF NOT EXISTS form_submissions (
                id VARCHAR(36) PRIMARY KEY,
                work_order_number VARCHAR(100),
                form_type VARCHAR(50),
                form_data JSON,
                pdf_s3_key VARCHAR(255),
                status VARCHAR(20) DEFAULT 'draft',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `;
        
        await connection.execute(createTableSQL);
        console.log('✅ Tabla form_submissions creada');
        
        // Verificar tabla
        const [rows] = await connection.execute('SHOW TABLES');
        console.log('📋 Tablas disponibles:', rows);
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

createTables();