:root {
    --viva-green: #00a650;
    --viva-light-green: #8dc63f;
    --card-teal: #008080;
    --light-gray: #f8f9fa;
    --dark-gray: #343a40;
    --white: #ffffff;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    background-color: var(--light-gray);
}

.header {
    background-color: var(--white);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.logo {
    height: 40px;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-weight: 500;
    color: var(--dark-gray);
}

.user-icon {
    width: 30px;
    height: 30px;
    background-color: var(--dark-gray);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 2rem;
}

.title {
    color: var(--dark-gray);
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 3px;
    background-color: var(--viva-green);
}

.search-container {
    margin-bottom: 1.5rem;
}

.search-input {
    width: 100%;
    padding: 0.8rem 1.2rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s;
}

.search-input:focus {
    border-color: var(--viva-green);
    box-shadow: 0 0 0 3px rgba(0, 166, 80, 0.1);
}

.forms-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-card {
    background-color: var(--white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    animation: fadeIn 0.5s ease-out forwards;
}

.form-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.form-card-header {
    background-color: var(--card-teal);
    height: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.form-card-header::after {
    content: '\f15c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 400;
    font-size: 3rem;
    color: rgba(255, 255, 255, 0.3);
}

.form-card-body {
    padding: 1.2rem;
}

.form-title {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--dark-gray);
}

.form-subtitle {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Footer */
.footer {
    background-color: var(--dark-gray);
    color: var(--white);
    text-align: center;
    padding: 1.5rem;
    margin-top: 3rem;
}

.footer-logo {
    height: 30px;
    margin-bottom: 1rem;
}

.copyright {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Responsive */
@media (max-width: 768px) {
    .forms-container {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: 0 1rem;
    }
    
    .header {
        padding: 1rem;
    }
}

/* Animaciones */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-card:nth-child(1) { animation-delay: 0.1s; }
.form-card:nth-child(2) { animation-delay: 0.2s; }
.form-card:nth-child(3) { animation-delay: 0.3s; }
.form-card:nth-child(4) { animation-delay: 0.4s; }
.form-card:nth-child(5) { animation-delay: 0.5s; }
.form-card:nth-child(6) { animation-delay: 0.6s; }

/* ====================================================
   ESTILOS PARA FORMULARIOS ENVIADOS
   ================================================== */

/* Sistema de pestañas */
.section-tabs {
    display: flex;
    border-bottom: 2px solid #e0e0e0;
    margin-bottom: 2rem;
    background-color: var(--white);
    border-radius: 8px 8px 0 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-btn {
    flex: 1;
    padding: 1rem 2rem;
    background: transparent;
    border: none;
    cursor: pointer;
    font-weight: 500;
    font-size: 1rem;
    color: #6c757d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.tab-btn:hover {
    background-color: #f8f9fa;
    color: var(--viva-green);
}

.tab-btn.active {
    color: var(--viva-green);
    border-bottom-color: var(--viva-green);
    background-color: rgba(0, 166, 80, 0.05);
}

.tab-btn i {
    font-size: 1.1rem;
}

/* Contenido de secciones */
.section-content {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

.section-content.hidden {
    display: none;
}

/* Formularios enviados */
.submitted-forms-container {
    background-color: var(--white);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    min-height: 300px;
}

.submitted-forms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.form-card.submitted-form {
    border-left: 4px solid var(--viva-green);
    background: linear-gradient(135deg, #fff 0%, #f8fff8 100%);
}

.form-card.submitted-form:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 166, 80, 0.15);
}

.form-card-header.submitted {
    background: linear-gradient(135deg, var(--viva-green), var(--viva-light-green));
    height: 100px;
}

.form-card-header.submitted::after {
    content: '\f1c1';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 2.5rem;
    color: rgba(255, 255, 255, 0.4);
}

.form-metadata {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.8rem;
    padding-top: 0.8rem;
    border-top: 1px solid #e9ecef;
}

.form-metadata .metadata-item {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.form-metadata .metadata-item i {
    font-size: 0.8rem;
    color: var(--viva-green);
}

.form-actions {
    display: flex;
    gap: 0.8rem;
    margin-top: 1rem;
}

.btn-edit, .btn-view-pdf {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    justify-content: center;
}

.btn-edit {
    background: var(--viva-green);
    color: white;
}

.btn-edit:hover {
    background: #008a43;
    transform: translateY(-1px);
}

.btn-view-pdf {
    background: #6c757d;
    color: white;
}

.btn-view-pdf:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.form-status {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-status.submitted {
    background: rgba(0, 166, 80, 0.1);
    color: var(--viva-green);
}

.form-status.draft {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
}

/* Estados de carga */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #6c757d;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--viva-green);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mensaje sin formularios */
.no-forms-message {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.no-forms-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dee2e6;
}

.no-forms-message h3 {
    color: var(--dark-gray);
    margin-bottom: 0.5rem;
}

/* Responsive para formularios enviados */
@media (max-width: 768px) {
    .submitted-forms-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn-edit, .btn-view-pdf {
        flex: none;
    }
    
    .form-metadata {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .tab-btn {
        padding: 0.8rem 1rem;
        font-size: 0.9rem;
    }
}

/* Animaciones adicionales */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.form-card.submitted-form {
    animation: slideIn 0.5s ease-out forwards;
}

.form-card.submitted-form:nth-child(1) { animation-delay: 0.1s; }
.form-card.submitted-form:nth-child(2) { animation-delay: 0.2s; }
.form-card.submitted-form:nth-child(3) { animation-delay: 0.3s; }
.form-card.submitted-form:nth-child(4) { animation-delay: 0.4s; }
.form-card.submitted-form:nth-child(5) { animation-delay: 0.5s; }
.form-card.submitted-form:nth-child(6) { animation-delay: 0.6s; } 