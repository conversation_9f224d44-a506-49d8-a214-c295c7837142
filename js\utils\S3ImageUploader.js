/**
 * ====================================================
 * S3 IMAGE UPLOADER
 * Manejo específico de subida de imágenes a AWS S3
 * ====================================================
 */

class S3ImageUploader {
    constructor() {
        this.uploadQueue = [];
        this.maxConcurrentUploads = 3;
        this.activeUploads = 0;
        this.retryAttempts = 3;
        this.retryDelay = 1000; // 1 segundo
        
        this.init();
    }

    init() {
        console.log('☁️ Iniciando S3ImageUploader...');
        console.log(`🔄 Máximo de subidas concurrentes: ${this.maxConcurrentUploads}`);
    }

    // ====================================================
    // MÉTODOS PRINCIPALES
    // ====================================================

    /**
     * Subir imagen a S3
     * @param {File} file - Archivo de imagen
     * @param {Object} options - Opciones de subida
     * @returns {Promise<Object>} - Resultado de la subida
     */
    async uploadImage(file, options = {}) {
        const {
            mode = 'draft',
            formId = null,
            folder = 'images',
            generateThumbnail = false
        } = options;

        console.log(`☁️ Subiendo imagen a S3: ${file.name} [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}]`);

        // Generar key única para S3
        const s3Key = this.generateS3Key(file, mode, formId, folder);
        
        // Crear FormData
        const formData = new FormData();
        formData.append('image', file);
        formData.append('key', s3Key);
        formData.append('mode', mode);
        formData.append('generateThumbnail', generateThumbnail.toString());
        
        if (formId) {
            formData.append('formId', formId);
        }

        // Subir con reintentos
        return await this.uploadWithRetry(formData, file.name);
    }

    /**
     * Subir múltiples imágenes con control de concurrencia
     * @param {Array} files - Array de archivos
     * @param {Object} options - Opciones de subida
     * @returns {Promise<Array>} - Array de resultados
     */
    async uploadMultipleImages(files, options = {}) {
        console.log(`☁️ Subiendo ${files.length} imágenes a S3 con control de concurrencia...`);
        
        const results = [];
        const promises = [];
        
        for (const file of files) {
            const promise = this.queueUpload(file, options);
            promises.push(promise);
        }
        
        // Esperar a que todas las subidas terminen
        const uploadResults = await Promise.allSettled(promises);
        
        uploadResults.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                results.push({
                    file: files[index],
                    success: true,
                    data: result.value
                });
            } else {
                results.push({
                    file: files[index],
                    success: false,
                    error: result.reason
                });
            }
        });
        
        return results;
    }

    /**
     * Agregar subida a la cola
     * @param {File} file - Archivo a subir
     * @param {Object} options - Opciones de subida
     * @returns {Promise} - Promesa de subida
     */
    async queueUpload(file, options) {
        return new Promise((resolve, reject) => {
            this.uploadQueue.push({
                file,
                options,
                resolve,
                reject
            });
            
            this.processQueue();
        });
    }

    /**
     * Procesar cola de subidas
     */
    async processQueue() {
        if (this.activeUploads >= this.maxConcurrentUploads || this.uploadQueue.length === 0) {
            return;
        }
        
        const upload = this.uploadQueue.shift();
        this.activeUploads++;
        
        try {
            const result = await this.uploadImage(upload.file, upload.options);
            upload.resolve(result);
        } catch (error) {
            upload.reject(error);
        } finally {
            this.activeUploads--;
            this.processQueue(); // Procesar siguiente en cola
        }
    }

    /**
     * Subir con reintentos
     * @param {FormData} formData - Datos del formulario
     * @param {string} fileName - Nombre del archivo
     * @returns {Promise<Object>} - Resultado de la subida
     */
    async uploadWithRetry(formData, fileName) {
        let lastError;
        
        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                console.log(`☁️ Intento ${attempt}/${this.retryAttempts} para ${fileName}`);
                
                const response = await fetch('/api/upload-image-s3', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || `HTTP ${response.status}`);
                }
                
                const result = await response.json();
                
                console.log(`✅ Imagen subida exitosamente: ${fileName}`);
                return result;
                
            } catch (error) {
                lastError = error;
                console.warn(`⚠️ Error en intento ${attempt} para ${fileName}:`, error.message);
                
                // Esperar antes del siguiente intento
                if (attempt < this.retryAttempts) {
                    await this.delay(this.retryDelay * attempt);
                }
            }
        }
        
        console.error(`❌ Error definitivo subiendo ${fileName}:`, lastError);
        throw lastError;
    }

    // ====================================================
    // MÉTODOS UTILITARIOS
    // ====================================================

    /**
     * Generar key única para S3
     * @param {File} file - Archivo
     * @param {string} mode - Modo ('draft' o 'edit')
     * @param {string} formId - ID del formulario
     * @param {string} folder - Carpeta base
     * @returns {string} - Key para S3
     */
    generateS3Key(file, mode, formId, folder) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const randomId = Math.random().toString(36).substring(2, 8);
        
        // Limpiar nombre del archivo
        const cleanFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
        
        if (mode === 'edit' && formId) {
            return `${folder}/${mode}/${formId}/${timestamp}_${randomId}_${cleanFileName}`;
        } else {
            return `${folder}/${mode}/${timestamp}_${randomId}_${cleanFileName}`;
        }
    }

    /**
     * Validar respuesta de S3
     * @param {Object} response - Respuesta del servidor
     * @returns {boolean} - True si es válida
     */
    validateS3Response(response) {
        return response && 
               response.success && 
               response.key && 
               response.url;
    }

    /**
     * Delay para reintentos
     * @param {number} ms - Milisegundos a esperar
     * @returns {Promise} - Promesa que se resuelve tras el delay
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Obtener información del bucket
     * @returns {Promise<Object>} - Información del bucket
     */
    async getBucketInfo() {
        try {
            const response = await fetch('/api/s3-info');
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            return await response.json();
            
        } catch (error) {
            console.error('❌ Error obteniendo información del bucket:', error);
            throw error;
        }
    }

    /**
     * Verificar si una imagen existe en S3
     * @param {string} s3Key - Key de la imagen en S3
     * @returns {Promise<boolean>} - True si existe
     */
    async checkImageExists(s3Key) {
        try {
            const response = await fetch(`/api/check-s3-image/${encodeURIComponent(s3Key)}`);
            return response.ok;
        } catch (error) {
            console.error('❌ Error verificando imagen en S3:', error);
            return false;
        }
    }

    /**
     * Eliminar imagen de S3
     * @param {string} s3Key - Key de la imagen en S3
     * @returns {Promise<boolean>} - True si se eliminó
     */
    async deleteImage(s3Key) {
        try {
            const response = await fetch(`/api/delete-s3-image/${encodeURIComponent(s3Key)}`, {
                method: 'DELETE'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            console.log(`🗑️ Imagen eliminada de S3: ${s3Key}`);
            return true;
            
        } catch (error) {
            console.error('❌ Error eliminando imagen de S3:', error);
            return false;
        }
    }

    /**
     * Obtener URL presignada para imagen
     * @param {string} s3Key - Key de la imagen en S3
     * @param {number} expiresIn - Tiempo de expiración en segundos
     * @returns {Promise<string>} - URL presignada
     */
    async getPresignedUrl(s3Key, expiresIn = 3600) {
        try {
            const response = await fetch('/api/get-presigned-url', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    key: s3Key,
                    expiresIn
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            return data.url;
            
        } catch (error) {
            console.error('❌ Error obteniendo URL presignada:', error);
            throw error;
        }
    }

    // ====================================================
    // MÉTODOS DE MONITOREO
    // ====================================================

    /**
     * Obtener estadísticas de subidas
     * @returns {Object} - Estadísticas
     */
    getUploadStats() {
        return {
            activeUploads: this.activeUploads,
            queuedUploads: this.uploadQueue.length,
            maxConcurrentUploads: this.maxConcurrentUploads,
            retryAttempts: this.retryAttempts,
            retryDelay: this.retryDelay
        };
    }

    /**
     * Limpiar cola de subidas
     */
    clearQueue() {
        this.uploadQueue.forEach(upload => {
            upload.reject(new Error('Cola de subidas limpiada'));
        });
        
        this.uploadQueue = [];
        console.log('🧹 Cola de subidas limpiada');
    }

    /**
     * Actualizar configuración
     * @param {Object} config - Nueva configuración
     */
    updateConfig(config) {
        if (config.maxConcurrentUploads) {
            this.maxConcurrentUploads = config.maxConcurrentUploads;
        }
        
        if (config.retryAttempts) {
            this.retryAttempts = config.retryAttempts;
        }
        
        if (config.retryDelay) {
            this.retryDelay = config.retryDelay;
        }
        
        console.log('⚙️ Configuración de S3 actualizada:', config);
    }

    // ====================================================
    // MÉTODOS DE COMPRESIÓN (OPCIONAL)
    // ====================================================

    /**
     * Comprimir imagen antes de subir
     * @param {File} file - Archivo original
     * @param {number} quality - Calidad de compresión (0-1)
     * @returns {Promise<File>} - Archivo comprimido
     */
    async compressImage(file, quality = 0.8) {
        return new Promise((resolve, reject) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                // Calcular dimensiones manteniendo aspect ratio
                const maxWidth = 1920;
                const maxHeight = 1080;
                
                let { width, height } = img;
                
                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    width *= ratio;
                    height *= ratio;
                }
                
                canvas.width = width;
                canvas.height = height;
                
                // Dibujar imagen redimensionada
                ctx.drawImage(img, 0, 0, width, height);
                
                // Convertir a blob
                canvas.toBlob((blob) => {
                    if (blob) {
                        const compressedFile = new File([blob], file.name, {
                            type: file.type,
                            lastModified: Date.now()
                        });
                        
                        console.log(`🗜️ Imagen comprimida: ${file.name} (${file.size} → ${compressedFile.size} bytes)`);
                        resolve(compressedFile);
                    } else {
                        reject(new Error('Error comprimiendo imagen'));
                    }
                }, file.type, quality);
            };
            
            img.onerror = reject;
            img.src = URL.createObjectURL(file);
        });
    }
}

// Crear instancia global
const s3ImageUploader = new S3ImageUploader();

// Hacer disponible globalmente
window.s3ImageUploader = s3ImageUploader;

console.log('✅ S3ImageUploader inicializado'); 