# Diagramas del Sistema VIVA_INSPECTORES

Esta carpeta contiene los diagramas de arquitectura del sistema en formato Mermaid.

## Archivos
- `diagrama_componentes.md` - Diagrama de componentes y arquitectura general
- `diagrama_flujo_datos.md` - Diagrama de flujo de datos secuencial
- `diagrama_entidad_relacion.md` - Diagrama Entidad-Relación de la base de datos
- `viva_inspectores_dbml.dbml` - **DBML para dbdiagram.io** (recomendado)
- `instrucciones_dbdiagram.md` - **Guía completa para usar dbdiagram.io**

## Cómo usar los diagramas

### Opción 1: dbdiagram.io (RECOMENDADO para ERD) ⭐
1. Visita: https://dbdiagram.io/
2. Crear nueva cuenta gratuita o login
3. Click "Import" → "From DBML"
4. Copia el contenido completo de `viva_inspectores_dbml.dbml`
5. <PERSON>ega en el editor
6. ¡El diagrama ERD se genera automáticamente!
7. Exporta como PNG, SVG, PDF o SQL

### Opción 2: Mermaid Live Editor (Para diagramas .md)
1. Visita: https://mermaid.live/
2. Copia el código del diagrama (sin los ``` markdown)
3. Pega en el editor
4. Descarga como PNG, SVG o PDF

### Opción 2: Mermaid CLI
```bash
npm install -g @mermaid-js/mermaid-cli
mmdc -i diagrama_componentes.md -o diagrama_componentes.png
mmdc -i diagrama_flujo_datos.md -o diagrama_flujo_datos.png
```

### Opción 3: VS Code con extensión
1. Instala extensión "Mermaid Markdown Syntax Highlighting"
2. Abre el archivo .md
3. Usa Ctrl+Shift+P → "Mermaid: Export Current Diagram"

### Opción 4: Screenshot
- Abre los archivos .md en cualquier visor que soporte Mermaid
- Toma screenshot del diagrama renderizado

## Características del archivo DBML

### ✅ Ventajas de usar viva_inspectores_dbml.dbml:
- **Visualización profesional** automática en dbdiagram.io
- **Colores específicos** por tipo de tabla (inspectores=azul, aeronaves=verde, etc.)
- **Documentación integrada** con notas en cada campo
- **Relaciones precisas** con cardinalidades correctas
- **Exportación múltiple**: PNG, SVG, PDF, SQL DDL
- **Colaboración en línea** - puedes compartir el diagrama fácilmente
- **Sincronización de esquema** - actualiza el DBML y el diagrama se actualiza automáticamente

### 📊 Estructura del DBML:
- **8 tablas principales** con todos los atributos
- **5 enums** para valores controlados
- **Índices optimizados** para MariaDB
- **Relaciones FK** claramente definidas
- **Documentación completa** en cada campo
- **Configuración de proyecto** específica para MariaDB

## Integración en documentos
Los archivos .md pueden incluirse directamente en GitHub, GitLab, o cualquier sistema que soporte Mermaid.
El archivo .dbml es la **versión definitiva** para presentaciones profesionales y desarrollo de base de datos. 