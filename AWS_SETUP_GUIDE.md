# 🛠️ Guía de Configuración AWS para VIVA Inspectores

## 📋 Resumen de Problemas Corregidos

### ❌ Problemas Encontrados:
1. **AWS en modo desarrollo**: `configureAWS.js` tenía `development: true`, deshabilitando AWS completamente
2. **Mock de AWS fallaba**: El mock simulaba errores en lugar de éxito
3. **Falta de fallback robusto**: No había manejo adecuado cuando AWS fallaba
4. **Sin diagnóstico**: No había herramientas para identificar problemas

### ✅ Soluciones Implementadas:
1. **Configuración AWS mejorada**: Modo desarrollo con mock funcional
2. **Sistema de fallback**: Almacenamiento local automático cuando AWS falla
3. **Manejo de errores**: `AWSErrorHandler` para gestión centralizada de errores
4. **Diagnóstico completo**: `SystemDiagnostic` para identificar problemas
5. **Carga de imágenes mejorada**: Soporte para imágenes locales y S3

## 🔧 Configuración para Producción

### 1. Configurar Credenciales AWS

Editar `configureAWS.js`:

```javascript
const AWS_CONFIG = {
    // CAMBIAR A FALSE PARA HABILITAR AWS EN PRODUCCIÓN
    development: false, // ✅ Cambiar a false
    
    // Configuración de AWS
    region: 'us-east-2',
    bucket: 'viva-inspectores-storage',
    
    // COMPLETAR CON CREDENCIALES REALES
    accessKeyId: 'TU_ACCESS_KEY_ID_REAL',
    secretAccessKey: 'TU_SECRET_ACCESS_KEY_REAL'
};
```

### 2. Crear Bucket S3

```bash
# Crear bucket
aws s3 mb s3://viva-inspectores-storage --region us-east-2

# Configurar CORS
aws s3api put-bucket-cors --bucket viva-inspectores-storage --cors-configuration file://cors.json
```

Archivo `cors.json`:
```json
{
    "CORSRules": [
        {
            "AllowedOrigins": ["*"],
            "AllowedHeaders": ["*"],
            "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
            "MaxAgeSeconds": 3000
        }
    ]
}
```

### 3. Configurar Políticas IAM

Crear usuario IAM con permisos:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::viva-inspectores-storage",
                "arn:aws:s3:::viva-inspectores-storage/*"
            ]
        }
    ]
}
```

## 🧪 Modo Desarrollo

### Características del Modo Desarrollo:
- ✅ AWS Mock funcional (simula éxito)
- ✅ Almacenamiento local automático
- ✅ Sin necesidad de credenciales reales
- ✅ Diagnóstico completo disponible

### Comandos de Diagnóstico:

```javascript
// Diagnóstico rápido
EmergencyDiagnostic.diagnose()

// Diagnóstico completo del sistema
runDiagnostic()

// Ver estado de AWS
getAWSStatus()

// Cambiar modo AWS (desarrollo/producción)
toggleAWSMode()

// Descargar reporte completo
downloadDiagnosticReport()
```

## 📊 Estructura de Archivos

```
VIVA_INSPECTORES/
├── configureAWS.js                 # ✅ Configuración AWS mejorada
├── js/utils/
│   ├── AWSErrorHandler.js          # 🆕 Manejo de errores AWS
│   ├── SystemDiagnostic.js         # 🆕 Diagnóstico completo
│   ├── ImageManager.js             # ✅ Actualizado con fallback
│   └── S3ImageUploader.js          # ✅ Existente
└── forms/
    └── BSI_PW1100.html            # ✅ Actualizado con nuevos scripts
```

## 🔍 Verificación del Sistema

### 1. Abrir Consola del Navegador

Al cargar la página, deberías ver:

```
🔧 configureAWS.js cargado
📊 Estado AWS: DESARROLLO (Mock) / PRODUCCIÓN
🛡️ Iniciando AWSErrorHandler...
✅ SystemDiagnostic inicializado
🖼️ Iniciando ImageManager...
🚨 DIAGNÓSTICO DE EMERGENCIA CARGADO
🔍 Ejecutando diagnóstico completo del sistema...
```

### 2. Verificar Estado AWS

```javascript
getAWSStatus()
// Resultado esperado:
// {
//   development: false,
//   region: "us-east-2", 
//   bucket: "viva-inspectores-storage",
//   sdkLoaded: true,
//   fallbackEnabled: true
// }
```

### 3. Probar Carga de Imágenes

1. Seleccionar imágenes en cualquier sección del formulario
2. Verificar en consola:
   - ✅ Subida exitosa a S3 (producción)
   - ✅ Guardado local exitoso (desarrollo/fallback)
3. Las imágenes deben aparecer en la previsualización

## 🚨 Solución de Problemas

### Problema: "AWS deshabilitado en desarrollo"
**Solución**: Cambiar `development: false` en `configureAWS.js`

### Problema: "Error subiendo a S3"
**Solución**: 
1. Verificar credenciales AWS
2. Verificar permisos del bucket
3. El sistema usará fallback local automáticamente

### Problema: "Imágenes no aparecen en PDF"
**Solución**:
1. Ejecutar `diagnoseImages()` en consola
2. Verificar que las imágenes tengan `uploaded: true`
3. El sistema cargará desde local si S3 falla

### Problema: "LocalStorage lleno"
**Solución**:
```javascript
// Limpiar archivos antiguos (más de 7 días)
awsErrorHandler.cleanupOldFiles(7)
```

## 📈 Monitoreo

### Ver Estadísticas de Errores:
```javascript
awsErrorHandler.getErrorStats()
```

### Ver Uso de LocalStorage:
```javascript
systemDiagnostic.diagnoseLocalStorage()
```

### Generar Reporte Completo:
```javascript
downloadDiagnosticReport()
```

## 🔄 Migración de Desarrollo a Producción

1. **Cambiar modo**: `development: false` en `configureAWS.js`
2. **Configurar credenciales**: Agregar ACCESS_KEY_ID y SECRET_ACCESS_KEY reales
3. **Crear bucket S3**: Seguir pasos de configuración AWS
4. **Probar conexión**: Ejecutar `testAWSConnection()`
5. **Verificar**: Ejecutar `runDiagnostic()` para confirmar todo funciona

## 📞 Soporte

Si encuentras problemas:

1. **Ejecutar diagnóstico**: `runDiagnostic()`
2. **Descargar reporte**: `downloadDiagnosticReport()`
3. **Revisar consola**: Buscar mensajes de error específicos
4. **Verificar configuración**: Usar `getAWSStatus()`

El sistema está diseñado para funcionar con o sin AWS, garantizando que las imágenes siempre se guarden localmente como respaldo.
