/**
 * ====================================================
 * LOCAL STORAGE MANAGER
 * Gestión inteligente de localStorage para formularios BSI PW1100
 * Separa datos de formularios nuevos vs editados
 * ====================================================
 */

class LocalStorageManager {
    constructor() {
        this.DRAFT_KEY = 'bsiFormData_draft';
        this.EDIT_KEY_PREFIX = 'bsiFormData_edit_';
        this.IMAGES_DRAFT_KEY = 'bsiFormData_images_draft';
        this.IMAGES_EDIT_KEY_PREFIX = 'bsiFormData_images_edit_';
        this.TIMESTAMP_SUFFIX = '_timestamp';
        this.CLEANUP_INTERVAL = 2 * 60 * 60 * 1000; // 2 horas en ms
        
        this.init();
    }

    init() {
        console.log('🗄️ Iniciando LocalStorageManager...');
        
        // Ejecutar limpieza inicial
        this.cleanup();
        
        // Configurar limpieza automática cada 30 minutos
        setInterval(() => {
            this.cleanup();
        }, 30 * 60 * 1000);
    }

    // ====================================================
    // MÉTODOS PRINCIPALES
    // ====================================================

    /**
     * Guardar datos del formulario según el contexto
     * @param {Object} data - Datos del formulario
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario (solo para modo edit)
     */
    saveFormData(data, mode = 'draft', formId = null) {
        try {
            const key = this.getFormDataKey(mode, formId);
            const timestampKey = key + this.TIMESTAMP_SUFFIX;
            
            // Agregar timestamp a los datos
            const dataWithTimestamp = {
                ...data,
                _saved_at: new Date().toISOString(),
                _mode: mode,
                _form_id: formId
            };
            
            localStorage.setItem(key, JSON.stringify(dataWithTimestamp));
            localStorage.setItem(timestampKey, new Date().toISOString());
            
            console.log(`💾 Datos guardados en localStorage [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}]`);
            
        } catch (error) {
            console.error('❌ Error guardando en localStorage:', error);
            
            // Si es error de espacio, intentar limpiar y reintentar
            if (error.name === 'QuotaExceededError') {
                this.cleanup();
                try {
                    const key = this.getFormDataKey(mode, formId);
                    localStorage.setItem(key, JSON.stringify(data));
                    console.log('💾 Datos guardados tras limpieza');
                } catch (retryError) {
                    console.error('❌ Error persistente en localStorage:', retryError);
                }
            }
        }
    }

    /**
     * Cargar datos del formulario según el contexto
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario (solo para modo edit)
     * @returns {Object|null} - Datos del formulario o null si no existe
     */
    loadFormData(mode = 'draft', formId = null) {
        try {
            const key = this.getFormDataKey(mode, formId);
            const data = localStorage.getItem(key);
            
            if (!data) {
                console.log(`📂 No hay datos guardados [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}]`);
                return null;
            }
            
            const formData = JSON.parse(data);
            
            // Verificar si los datos no han expirado
            if (this.isExpired(key)) {
                console.log(`⏰ Datos expirados, eliminando [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}]`);
                this.clearFormData(mode, formId);
                return null;
            }
            
            console.log(`📥 Datos cargados desde localStorage [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}]`);
            return formData;
            
        } catch (error) {
            console.error('❌ Error cargando desde localStorage:', error);
            return null;
        }
    }

    /**
     * Limpiar datos del formulario según el contexto
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario (solo para modo edit)
     */
    clearFormData(mode = 'draft', formId = null) {
        try {
            const key = this.getFormDataKey(mode, formId);
            const timestampKey = key + this.TIMESTAMP_SUFFIX;
            
            localStorage.removeItem(key);
            localStorage.removeItem(timestampKey);
            
            // También limpiar imágenes asociadas
            this.clearImages(mode, formId);
            
            console.log(`🗑️ Datos eliminados de localStorage [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}]`);
            
        } catch (error) {
            console.error('❌ Error limpiando localStorage:', error);
        }
    }

    // ====================================================
    // MÉTODOS PARA IMÁGENES
    // ====================================================

    /**
     * Guardar imágenes según el contexto
     * @param {Array} images - Array de objetos de imagen
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario (solo para modo edit)
     */
    saveImages(images, mode = 'draft', formId = null) {
        try {
            const key = this.getImagesKey(mode, formId);
            const timestampKey = key + this.TIMESTAMP_SUFFIX;
            
            // Filtrar imágenes por tamaño para localStorage
            const imagesForStorage = images.map(img => {
                // Si la imagen es muy grande, solo guardar metadatos
                if (img.size && img.size > 1024 * 1024) { // >1MB
                    return {
                        name: img.name,
                        description: img.description,
                        size: img.size,
                        type: img.type,
                        s3_key: img.s3_key,
                        s3_url: img.s3_url,
                        _large_file: true
                    };
                }
                
                // Imágenes pequeñas se guardan completas
                return {
                    name: img.name,
                    description: img.description,
                    size: img.size,
                    type: img.type,
                    src: img.src,
                    s3_key: img.s3_key,
                    s3_url: img.s3_url,
                    _large_file: false
                };
            });
            
            localStorage.setItem(key, JSON.stringify(imagesForStorage));
            localStorage.setItem(timestampKey, new Date().toISOString());
            
            console.log(`🖼️ Imágenes guardadas en localStorage [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}]: ${images.length} imágenes`);
            
        } catch (error) {
            console.error('❌ Error guardando imágenes:', error);
            
            // Si es error de espacio, guardar solo metadatos
            if (error.name === 'QuotaExceededError') {
                const metadataOnly = images.map(img => ({
                    name: img.name,
                    description: img.description,
                    size: img.size,
                    type: img.type,
                    s3_key: img.s3_key,
                    s3_url: img.s3_url,
                    _large_file: true
                }));
                
                try {
                    const key = this.getImagesKey(mode, formId);
                    localStorage.setItem(key, JSON.stringify(metadataOnly));
                    console.log('🖼️ Imágenes guardadas como metadatos tras error de espacio');
                } catch (retryError) {
                    console.error('❌ Error persistente guardando imágenes:', retryError);
                }
            }
        }
    }

    /**
     * Cargar imágenes según el contexto
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario (solo para modo edit)
     * @returns {Array} - Array de imágenes o array vacío
     */
    loadImages(mode = 'draft', formId = null) {
        try {
            const key = this.getImagesKey(mode, formId);
            const data = localStorage.getItem(key);
            
            if (!data) {
                console.log(`🖼️ No hay imágenes guardadas [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}]`);
                return [];
            }
            
            // Verificar si los datos no han expirado
            if (this.isExpired(key)) {
                console.log(`⏰ Imágenes expiradas, eliminando [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}]`);
                this.clearImages(mode, formId);
                return [];
            }
            
            const images = JSON.parse(data);
            console.log(`🖼️ Imágenes cargadas desde localStorage [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}]: ${images.length} imágenes`);
            
            return images;
            
        } catch (error) {
            console.error('❌ Error cargando imágenes:', error);
            return [];
        }
    }

    /**
     * Limpiar imágenes según el contexto
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario (solo para modo edit)
     */
    clearImages(mode = 'draft', formId = null) {
        try {
            const key = this.getImagesKey(mode, formId);
            const timestampKey = key + this.TIMESTAMP_SUFFIX;
            
            localStorage.removeItem(key);
            localStorage.removeItem(timestampKey);
            
            console.log(`🗑️ Imágenes eliminadas de localStorage [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}]`);
            
        } catch (error) {
            console.error('❌ Error limpiando imágenes:', error);
        }
    }

    // ====================================================
    // MÉTODOS UTILITARIOS
    // ====================================================

    /**
     * Obtener clave para datos del formulario
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     * @returns {string} - Clave para localStorage
     */
    getFormDataKey(mode, formId) {
        if (mode === 'edit' && formId) {
            return `${this.EDIT_KEY_PREFIX}${formId}`;
        }
        return this.DRAFT_KEY;
    }

    /**
     * Obtener clave para imágenes
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     * @returns {string} - Clave para localStorage
     */
    getImagesKey(mode, formId) {
        if (mode === 'edit' && formId) {
            return `${this.IMAGES_EDIT_KEY_PREFIX}${formId}`;
        }
        return this.IMAGES_DRAFT_KEY;
    }

    /**
     * Verificar si los datos han expirado
     * @param {string} key - Clave de los datos
     * @returns {boolean} - True si han expirado
     */
    isExpired(key) {
        try {
            const timestampKey = key + this.TIMESTAMP_SUFFIX;
            const timestamp = localStorage.getItem(timestampKey);
            
            if (!timestamp) return true;
            
            const savedTime = new Date(timestamp).getTime();
            const currentTime = new Date().getTime();
            
            return (currentTime - savedTime) > this.CLEANUP_INTERVAL;
            
        } catch (error) {
            console.error('❌ Error verificando expiración:', error);
            return true;
        }
    }

    /**
     * Limpieza automática de localStorage
     */
    cleanup() {
        try {
            console.log('🧹 Ejecutando limpieza de localStorage...');
            
            let cleanedCount = 0;
            const keys = Object.keys(localStorage);
            
            for (const key of keys) {
                // Limpiar datos expirados de formularios
                if (key.startsWith(this.DRAFT_KEY) || 
                    key.startsWith(this.EDIT_KEY_PREFIX) ||
                    key.startsWith(this.IMAGES_DRAFT_KEY) ||
                    key.startsWith(this.IMAGES_EDIT_KEY_PREFIX)) {
                    
                    if (this.isExpired(key)) {
                        localStorage.removeItem(key);
                        localStorage.removeItem(key + this.TIMESTAMP_SUFFIX);
                        cleanedCount++;
                    }
                }
            }
            
            if (cleanedCount > 0) {
                console.log(`🧹 Limpieza completada: ${cleanedCount} elementos eliminados`);
            }
            
        } catch (error) {
            console.error('❌ Error en limpieza de localStorage:', error);
        }
    }

    /**
     * Obtener estadísticas de uso de localStorage
     * @returns {Object} - Estadísticas de uso
     */
    getStorageStats() {
        try {
            const stats = {
                totalKeys: 0,
                draftKeys: 0,
                editKeys: 0,
                imageKeys: 0,
                expiredKeys: 0,
                totalSizeEstimate: 0
            };
            
            const keys = Object.keys(localStorage);
            
            for (const key of keys) {
                const value = localStorage.getItem(key);
                stats.totalSizeEstimate += (key.length + (value ? value.length : 0)) * 2; // Rough estimate
                
                if (key.startsWith(this.DRAFT_KEY) || 
                    key.startsWith(this.EDIT_KEY_PREFIX) ||
                    key.startsWith(this.IMAGES_DRAFT_KEY) ||
                    key.startsWith(this.IMAGES_EDIT_KEY_PREFIX)) {
                    
                    stats.totalKeys++;
                    
                    if (key.includes('draft')) stats.draftKeys++;
                    if (key.includes('edit')) stats.editKeys++;
                    if (key.includes('images')) stats.imageKeys++;
                    if (this.isExpired(key)) stats.expiredKeys++;
                }
            }
            
            stats.totalSizeMB = (stats.totalSizeEstimate / (1024 * 1024)).toFixed(2);
            
            return stats;
            
        } catch (error) {
            console.error('❌ Error obteniendo estadísticas:', error);
            return {};
        }
    }

    /**
     * Limpiar todo el localStorage relacionado con formularios
     */
    clearAll() {
        try {
            const keys = Object.keys(localStorage);
            let cleanedCount = 0;
            
            for (const key of keys) {
                if (key.startsWith(this.DRAFT_KEY) || 
                    key.startsWith(this.EDIT_KEY_PREFIX) ||
                    key.startsWith(this.IMAGES_DRAFT_KEY) ||
                    key.startsWith(this.IMAGES_EDIT_KEY_PREFIX)) {
                    
                    localStorage.removeItem(key);
                    cleanedCount++;
                }
            }
            
            console.log(`🧹 LocalStorage completamente limpiado: ${cleanedCount} elementos eliminados`);
            
        } catch (error) {
            console.error('❌ Error limpiando localStorage:', error);
        }
    }
}

// Crear instancia global
const localStorageManager = new LocalStorageManager();

// Hacer disponible globalmente
window.localStorageManager = localStorageManager;

console.log('✅ LocalStorageManager inicializado'); 