{"name": "forms-to-word-generator", "version": "1.0.0", "description": "Sistema para generar documentos Word desde formularios HTML de Vivaaerobus", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "analyze-template": "node analyze-template.js"}, "dependencies": {"aws-sdk": "^2.1692.0", "axios": "^1.9.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "docxtemplater": "^3.63.0", "dotenv": "^17.2.1", "express": "^4.18.2", "form-data": "^4.0.2", "fs-extra": "^11.1.1", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.1", "jspdf": "^3.0.1", "mssql": "^11.0.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.2", "node-fetch": "^3.3.2", "pizzip": "^3.1.4", "sharp": "^0.32.6", "util": "^0.12.5", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["forms", "word", "docx", "generator", "vivaaerobus"], "author": "Vivaaerobus", "license": "ISC"}