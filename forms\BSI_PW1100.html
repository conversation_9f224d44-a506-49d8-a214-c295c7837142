<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BSI Report Form (PW 1100 ENGINES)</title>
    <link rel="stylesheet" href="../css/forms-common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AWS SDK para subir a S3 -->
    <script src="https://sdk.amazonaws.com/js/aws-sdk-2.1563.0.min.js"></script>
    <script src="../configureAWS.js"></script>
    <!-- Librerías para generación de PDF -->
    <script src="https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js"></script>
    <!-- Plugin AutoTable para manejo de tablas multipágina -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>
    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <!-- NUEVO: Librería PDF.js para previsualización -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.10.111/pdf.min.js"></script>
    <script>
        // Configurar worker de PDF.js
        if (window.pdfjsLib) {
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.10.111/pdf.worker.min.js';
        }
    </script>
    
    <!-- Librerías de respaldo por si las primeras fallan -->
    <script>
        // Verificar si jsPDF se cargó correctamente, si no, cargar desde CDN alternativo
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (typeof window.jspdf === 'undefined' && typeof window.jsPDF === 'undefined') {
                    console.warn('⚠️ jsPDF principal no se cargó, intentando CDN alternativo...');
                    const script = document.createElement('script');
                    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
                    script.onload = function() {
                        console.log('✅ jsPDF cargado desde CDN alternativo');
                    };
                    script.onerror = function() {
                        console.error('❌ Error cargando jsPDF desde CDN alternativo');
                    };
                    document.head.appendChild(script);
                }
                
                if (typeof window.html2canvas === 'undefined') {
                    console.warn('⚠️ html2canvas principal no se cargó, intentando CDN alternativo...');
                    const script = document.createElement('script');
                    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
                    script.onload = function() {
                        console.log('✅ html2canvas cargado desde CDN alternativo');
                    };
                    script.onerror = function() {
                        console.error('❌ Error cargando html2canvas desde CDN alternativo');
                    };
                    document.head.appendChild(script);
                }
            }, 1000);
        });
    </script>
    <script src="../js/select-search.js"></script>
    <!-- Librería html2pdf.js (incluye html2canvas y jsPDF) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <!-- Herramientas de debug PDF -->
    <script src="../js/utils/PdfDebugger.js"></script>
    <script src="../js/utils/LocalStorageManager.js"></script>
    <script src="../js/utils/AWSErrorHandler.js"></script>
    <script src="../js/utils/SystemDiagnostic.js"></script>
    <script src="../js/utils/ImageManager.js"></script>
    <script src="../js/utils/S3ImageUploader.js"></script>
    <script src="../js/form-handlers/BSI_PW1100/PdfManager.js"></script>
    <script src="../js/form-handlers/BSI_PW1100_Handler.js"></script>
    
    <!-- DIAGNÓSTICO DE EMERGENCIA PARA PDF EN BLANCO -->
    <script>
        /**
         * 🚨 HERRAMIENTA DE DIAGNÓSTICO DE EMERGENCIA PARA PDF
         * Identifica exactamente por qué las páginas están en blanco
         */
        window.EmergencyDiagnostic = {
            // DIAGNÓSTICO PRINCIPAL
            diagnose() {
                console.group('🚨 DIAGNÓSTICO DE EMERGENCIA - PDF EN BLANCO');

                console.log('1. Verificando handler...');
                const handler = window.handler;
                if (!handler) {
                    console.error('❌ CRÍTICO: No hay handler activo');
                    console.log('💡 SOLUCIÓN: Recargar la página o verificar BSI_PW1100_Handler.js');
                    console.groupEnd();
                    return;
                }
                console.log('✅ Handler encontrado');

                console.log('2. Verificando formulario...');
                const form = document.querySelector('form');
                if (!form) {
                    console.error('❌ CRÍTICO: No hay formulario');
                    console.groupEnd();
                    return;
                }
                console.log('✅ Formulario encontrado');

                console.log('3. Verificando LocalStorageManager...');
                if (!window.localStorageManager) {
                    console.error('❌ CRÍTICO: LocalStorageManager no encontrado');
                    console.groupEnd();
                    return;
                }

                try {
                    const lsmStatus = window.localStorageManager.getStatus();
                    console.log('✅ LocalStorageManager funcionando:', lsmStatus);
                } catch (error) {
                    console.error('❌ ERROR en LocalStorageManager:', error);
                }

                console.log('4. Verificando ImageManager...');
                if (!window.imageManager) {
                    console.error('❌ CRÍTICO: ImageManager no encontrado');
                } else {
                    try {
                        const imgStats = window.imageManager.getStats();
                        console.log('✅ ImageManager funcionando:', imgStats);
                    } catch (error) {
                        console.error('❌ ERROR en ImageManager:', error);
                    }
                }

                console.log('5. Verificando datos del formulario...');
                let formData = {};
                try {
                    formData = handler.collectFormData();
                    const filledFields = Object.keys(formData).filter(k => formData[k] && formData[k] !== '').length;
                    console.log(`📊 Campos con datos: ${filledFields}/${Object.keys(formData).length}`);

                    if (filledFields === 0) {
                        console.error('❌ PROBLEMA ENCONTRADO: Formulario está VACÍO');
                        console.log('💡 SOLUCIÓN: Llenar el formulario antes de generar PDF');
                        console.log('🔧 Usar: EmergencyDiagnostic.fillTestData()');
                    } else {
                        console.log('✅ Formulario tiene datos');
                    }
                } catch (error) {
                    console.error('❌ ERROR al recolectar datos:', error);
                    console.groupEnd();
                    return;
                }

                console.log('6. Verificando estado del PDF...');
                console.log(`📄 PDF cargado: ${handler.currentPdfDoc ? 'Sí' : 'No'}`);
                console.log(`📊 Páginas PDF: ${handler.totalPdfPages || 0}`);
                if (!handler.currentPdfDoc) {
                    console.error('❌ PROBLEMA: No hay documento PDF cargado');
                    console.log('💡 SOLUCIÓN: Abrir modal de preview primero para generar PDF');
                }

                console.groupEnd();
                return { formData, filledFields: Object.keys(formData).filter(k => formData[k] && formData[k] !== '').length };
            },

            // DIAGNÓSTICO ESPECÍFICO DE IMÁGENES
            diagnoseImages() {
                console.group('📸 DIAGNÓSTICO ESPECÍFICO DE IMÁGENES');

                try {
                    // Verificar LocalStorageManager
                    if (!window.localStorageManager) {
                        console.error('❌ LocalStorageManager no disponible');
                        return;
                    }

                    // Verificar métodos disponibles
                    const methods = ['loadImages', 'saveImages', 'getImages', 'setImages'];
                    methods.forEach(method => {
                        if (typeof window.localStorageManager[method] === 'function') {
                            console.log(`✅ Método ${method} disponible`);
                        } else {
                            console.error(`❌ Método ${method} NO disponible`);
                        }
                    });

                    // Verificar ImageManager
                    if (!window.imageManager) {
                        console.error('❌ ImageManager no disponible');
                        return;
                    }

                    // Probar carga de imágenes
                    const urlParams = new URLSearchParams(window.location.search);
                    const mode = urlParams.get('edit') ? 'edit' : 'draft';
                    const formId = urlParams.get('edit') || null;

                    console.log(`🔍 Probando carga de imágenes [${mode}${formId ? `:${formId}` : ''}]...`);

                    const images = window.localStorageManager.loadImages(mode, formId);
                    console.log(`📊 Imágenes encontradas: ${images.length}`);

                    if (images.length > 0) {
                        images.forEach((img, index) => {
                            console.log(`📸 Imagen ${index + 1}: ${img.name} (sección: ${img.section || 'general'})`);
                        });
                    }

                    // Probar método getImagesBySection
                    try {
                        const testSection = 'lpc_stage1';
                        const sectionImages = window.imageManager.getImagesBySection(testSection, mode, formId);
                        console.log(`✅ getImagesBySection funcionando: ${sectionImages.length} imágenes en ${testSection}`);
                    } catch (error) {
                        console.error('❌ Error en getImagesBySection:', error);
                    }

                } catch (error) {
                    console.error('❌ Error en diagnóstico de imágenes:', error);
                } finally {
                    console.groupEnd();
                }
            },
            
            // LLENAR DATOS DE PRUEBA
            fillTestData() {
                console.log('🧪 Llenando formulario con datos de prueba...');
                
                const testData = {
                    'nombre_registrado': 'INSPECTOR DE PRUEBA',
                    'work_order_number': 'WO-TEST-001',
                    'date_of_bsi': '2024-01-15',
                    'inspected_by': 'Julio Acosta',
                    'inspector_stamp': 'QC-003',
                    'aircraft_registration': 'XA-TEST',
                    'aircraft_model': 'A320NEO',
                    'engine_sn': 'TEST123456'
                };
                
                let filled = 0;
                for (const [key, value] of Object.entries(testData)) {
                    const field = document.getElementById(key);
                    if (field) {
                        field.value = value;
                        filled++;
                        console.log(`✅ ${key} = ${value}`);
                    }
                }
                
                // Marcar radio buttons
                const radioSelections = {
                    'station': 'MTY',
                    'bsi_reason': 'Maintenance Program',
                    'bsi_type': 'HOT SECTION',
                    'aircraft_model': 'A320NEO',
                    'boroscope_type': 'Rigid'
                };
                
                for (const [name, value] of Object.entries(radioSelections)) {
                    const radio = document.querySelector(`input[name="${name}"][value="${value}"]`);
                    if (radio) {
                        radio.checked = true;
                        filled++;
                        console.log(`✅ Radio ${name} = ${value}`);
                    }
                }
                
                console.log(`✅ ${filled} campos llenados con datos de prueba`);
                return filled;
            },
            
            // PROBAR PDF DE EMERGENCIA
            async testEmergencyPdf() {
                console.log('🚨 PROBANDO PDF DE EMERGENCIA...');
                
                const testDiv = document.createElement('div');
                testDiv.innerHTML = `
                    <div style="width: 21cm; height: 29.7cm; padding: 2cm; background: white; font-family: Arial; color: black;">
                        <img src="img/Viva_Logo.svg.png" style="width: 100px; height: auto; margin-bottom: 20px;">
                        <h1 style="color: #00aa00;">✅ PRUEBA DE PDF EXITOSA</h1>
                        <h2>VIVA AEROBUS - SISTEMA DE INSPECCIÓN</h2>
                        <p><strong>Fecha y hora:</strong> ${new Date().toLocaleString()}</p>
                        <p><strong>Estado:</strong> Sistema de PDF funcionando correctamente</p>
                        
                        <h3>DIAGNÓSTICO COMPLETO:</h3>
                        <ul>
                            <li>✅ html2pdf.js está funcionando</li>
                            <li>✅ html2canvas está funcionando</li>
                            <li>✅ jsPDF está funcionando</li>
                            <li>✅ El navegador es compatible</li>
                        </ul>
                        
                        <h3>CONCLUSIÓN:</h3>
                        <p style="color: red; font-weight: bold;">
                            Si puedes ver este PDF con contenido, entonces el problema NO es el sistema de PDF.
                            El problema está en que el formulario está VACÍO o los datos no se están inyectando en las plantillas.
                        </p>
                        
                        <h3>PRÓXIMOS PASOS:</h3>
                        <ol>
                            <li>Llenar el formulario completamente</li>
                            <li>Verificar que todos los campos requeridos estén llenos</li>
                            <li>Abrir el modal de preview ANTES de generar PDF</li>
                            <li>Verificar que las páginas muestren los datos en el modal</li>
                            <li>Luego intentar generar el PDF</li>
                        </ol>
                    </div>
                `;
                
                testDiv.style.cssText = 'position: absolute; top: -10000px; left: -10000px;';
                document.body.appendChild(testDiv);
                
                try {
                    const options = {
                        margin: [5, 5, 5, 5],
                        filename: 'DIAGNOSTIC_TEST_VIVA.pdf',
                        image: { type: 'jpeg', quality: 0.98 },
                        html2canvas: { scale: 1.5, useCORS: true, backgroundColor: '#ffffff' },
                        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
                    };
                    
                    await window.html2pdf().set(options).from(testDiv).save();
                    console.log('✅ PDF DE EMERGENCIA GENERADO EXITOSAMENTE');
                    console.log('🎯 CONCLUSIÓN: El sistema de PDF FUNCIONA. El problema está en los DATOS DEL FORMULARIO.');
                    
                } catch (error) {
                    console.error('❌ Error generando PDF de emergencia:', error);
                    console.log('🚨 PROBLEMA CRÍTICO: El sistema de PDF no funciona');
                } finally {
                    document.body.removeChild(testDiv);
                }
            }
        };

        // Auto-ejecutar cuando la página esté lista
        window.addEventListener('load', function() {
            setTimeout(function() {
                console.log('🚨 DIAGNÓSTICO DE EMERGENCIA CARGADO');
                console.log('📋 COMANDOS DISPONIBLES:');
                console.log('  - EmergencyDiagnostic.diagnose() - Diagnóstico rápido');
                console.log('  - EmergencyDiagnostic.diagnoseImages() - Diagnóstico de imágenes');
                console.log('  - EmergencyDiagnostic.fillTestData() - Llenar datos de prueba');
                console.log('  - EmergencyDiagnostic.testEmergencyPdf() - Probar PDF directo');
                console.log('  - runDiagnostic() - Diagnóstico completo del sistema');
                console.log('  - getAWSStatus() - Ver estado de AWS');
                console.log('  - checkLocalStorageManager() - Verificar LocalStorageManager');
                console.log('  - downloadDiagnosticReport() - Descargar reporte completo');
                console.log('');
                console.log('🔥 EJECUTANDO DIAGNÓSTICO AUTOMÁTICO...');

                // Ejecutar diagnóstico básico primero
                if (window.EmergencyDiagnostic) {
                    window.EmergencyDiagnostic.diagnose();

                    // Ejecutar diagnóstico específico de imágenes
                    setTimeout(function() {
                        window.EmergencyDiagnostic.diagnoseImages();
                    }, 500);
                }

                // Luego ejecutar diagnóstico completo
                setTimeout(function() {
                    if (window.runDiagnostic) {
                        console.log('🔍 Ejecutando diagnóstico completo del sistema...');
                        window.runDiagnostic();
                    }
                }, 1500);
            }, 2000);
        });
    </script>
</head>
<body>
    <header class="header">
        <a href="../index.html" style="text-decoration: none;">
            <img src="../img/Viva_Logo.svg.png" alt="Vivaaerobus Logo" class="logo">
        </a>
        <div class="user-profile">
            <span>Inspector A</span>
            <div class="user-icon">
                <i class="fas fa-user" style="color: black;"></i>
            </div>
        </div>
    </header>

    <div class="form-container">
        <h1>BSI Report Form (PW 1100 ENGINES)</h1>
        <p class="form-note header-note">(F-QC-018 Rev 2) (Copia)</p>
        <p class="form-note"><span class="asterisk">*</span> Obligatoria</p>

        <form action="#" method="post">

            <div class="form-group">
                <label for="nombre_registrado"><span class="asterisk">*</span> Este formulario registrará su nombre, escriba su nombre.</label>
                <input type="text" id="nombre_registrado" name="nombre_registrado" required>
            </div>

            <fieldset>
                <legend>General Information</legend>

                <div class="form-group">
                    <label for="work_order_number">1. Work Order Number <span class="asterisk">*</span></label>
                    <input type="text" id="work_order_number" name="work_order_number" required>
                </div>

                <div class="form-group">
                    <label for="date_of_bsi">2. Date of BSI <span class="asterisk">*</span></label>
                    <input type="date" id="date_of_bsi" name="date_of_bsi" required>
                </div>

                <div class="form-group">
                    <label for="inspected_by">3. Inspected By <span class="asterisk">*</span></label>
                    <select id="inspected_by" name="inspected_by" required>
                        <option value="" disabled selected>Seleccione un inspector</option>
                        <option value="Julio Acosta">Julio Acosta</option>
                        <option value="Fabian Cavazos">Fabian Cavazos</option>
                        <option value="Daniel Cerino">Daniel Cerino</option>
                        <option value="Roberto Diaz">Roberto Diaz</option>
                        <option value="Abel Garrido">Abel Garrido</option>
                        <option value="Manuel Gonzalez">Manuel Gonzalez</option>
                        <option value="Mariano Iracheta">Mariano Iracheta</option>
                        <option value="Marcos Miranda">Marcos Miranda</option>
                        <option value="Carlos Ramirez">Carlos Ramirez</option>
                        <option value="Raul Ramirez">Raul Ramirez</option>
                        <option value="Andre Richaud">Andre Richaud</option>
                        <option value="Armando Rodarte">Armando Rodarte</option>
                        <option value="Victor Rubio">Victor Rubio</option>
                        <option value="Daniel Sala">Daniel Sala</option>
                        <option value="Lorena Ugalde">Lorena Ugalde</option>
                        <option value="Francisco Ayala">Francisco Ayala</option>
                        <option value="Reynol Aguilar">Reynol Aguilar</option>
                        <option value="Luis A Brambila">Luis A Brambila</option>
                        <option value="Oscar Sanchez">Oscar Sanchez</option>
                        <option value="Juan Gabriel Garcia">Juan Gabriel Garcia</option>
                        <option value="Omar Vicenteño">Omar Vicenteño</option>
                        <option value="Kenton Gonzalez">Kenton Gonzalez</option>
                        <option value="Roberto Macias">Roberto Macias</option>
                        <option value="Juan Cabello">Juan Cabello</option>
                        <option value="Florentino de Jesus">Florentino de Jesus</option>
                        <option value="Carlos Erick Ruiz">Carlos Erick Ruiz</option>
                        <option value="Adán Rendón">Adán Rendón</option>
                        <option value="Rafael Mendoza">Rafael Mendoza</option>
                        <option value="Jose Angel Monaco">Jose Angel Monaco</option>
                        <option value="Hector Hugo Marin">Hector Hugo Marin</option>
                        <option value="Alejandro Marcos López">Alejandro Marcos López</option>
                        <option value="Fernando Herrera">Fernando Herrera</option>
                        <option value="Rodrigo Aguilera">Rodrigo Aguilera</option>
                        <option value="Juan Jose Briones">Juan Jose Briones</option>
                        <option value="Jose Roberto Barrera">Jose Roberto Barrera</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="inspector_stamp">4. Inspector Stamp <span class="asterisk">*</span></label>
                    <select id="inspector_stamp" name="inspector_stamp" required>
                        <option value="" disabled selected>Seleccione un sello</option>
                        <option value="QC-003">QC-003</option>
                        <option value="QC-006">QC-006</option>
                        <option value="QC-007">QC-007</option>
                        <option value="QC-008">QC-008</option>
                        <option value="QC-014">QC-014</option>
                        <option value="QC-016">QC-016</option>
                        <option value="QC-019">QC-019</option>
                        <option value="QC-020">QC-020</option>
                        <option value="QC-021">QC-021</option>
                        <option value="QC-024">QC-024</option>
                        <option value="QC-025">QC-025</option>
                        <option value="QC-027">QC-027</option>
                        <option value="QC-028">QC-028</option>
                        <option value="QC-030">QC-030</option>
                        <option value="QC-032">QC-032</option>
                        <option value="QC-033">QC-033</option>
                        <option value="QC-034">QC-034</option>
                        <option value="QC-041">QC-041</option>
                        <option value="QC-044">QC-044</option>
                        <option value="QC-045">QC-045</option>
                        <option value="QC-049">QC-049</option>
                        <option value="QC-052">QC-052</option>
                        <option value="QC-054">QC-054</option>
                        <option value="QC-059">QC-059</option>
                        <option value="QC-058">QC-058</option>
                        <option value="QC-060">QC-060</option>
                        <option value="QC-062">QC-062</option>
                        <option value="QC-072">QC-072</option>
                        <option value="QC-078">QC-078</option>
                        <option value="QC-089">QC-089</option>
                        <option value="QC-101">QC-101</option>
                        <option value="QC-097">QC-097</option>
                        <option value="QC-100">QC-100</option>
                        <option value="QC-066">QC-066</option>
                    </select>
                </div>

                <div class="form-group">
                    <p class="radio-group-label">5. Station <span class="asterisk">*</span></p>
                    <div class="radio-group">
                        <div><input type="radio" id="station_mty" name="station" value="MTY" required><label for="station_mty">MTY</label></div>
                        <div><input type="radio" id="station_gdl" name="station" value="GDL"><label for="station_gdl">GDL</label></div>
                        <div><input type="radio" id="station_mex" name="station" value="MEX"><label for="station_mex">MEX</label></div>
                        <div><input type="radio" id="station_cun" name="station" value="CUN"><label for="station_cun">CUN</label></div>
                        <div><input type="radio" id="station_tij" name="station" value="TIJ"><label for="station_tij">TIJ</label></div>
                        <div><input type="radio" id="station_otras" name="station" value="Otras"><label for="station_otras">Otras</label></div>
                    </div>
                </div>

                <div class="form-group">
                    <p class="radio-group-label">6. BSI accomplished reason <span class="asterisk">*</span></p>
                    <div class="radio-group">
                        <div><input type="radio" id="bsi_reason_maintenance" name="bsi_reason" value="Maintenance Program" required><label for="bsi_reason_maintenance">Maintenance Program</label></div>
                        <div><input type="radio" id="bsi_reason_bird_strike" name="bsi_reason" value="Bird Strike"><label for="bsi_reason_bird_strike">Bird Strike</label></div>
                        <div><input type="radio" id="bsi_reason_engine_acceptance" name="bsi_reason" value="Engine Acceptance"><label for="bsi_reason_engine_acceptance">Engine Acceptance</label></div>
                        <div><input type="radio" id="bsi_reason_delivery_requirement" name="bsi_reason" value="Delivery Requirement"><label for="bsi_reason_delivery_requirement">Delivery Requirement</label></div>
                        <div><input type="radio" id="bsi_reason_troubleshooting" name="bsi_reason" value="Trouble Shooting"><label for="bsi_reason_troubleshooting">Trouble Shooting</label></div>
                    </div>
                </div>

                <div class="form-group">
                    <p class="radio-group-label">7. Type of BSI <span class="asterisk">*</span></p>
                    <div class="radio-group">
                        <div><input type="radio" id="bsi_type_full" name="bsi_type" value="Full BSI" required><label for="bsi_type_full">Full BSI</label></div>
                        <div><input type="radio" id="bsi_type_combustion" name="bsi_type" value="Combustion Chamber"><label for="bsi_type_combustion">Combustion Chamber</label></div>
                        <div><input type="radio" id="bsi_type_hpt" name="bsi_type" value="High Pressure Turbine"><label for="bsi_type_hpt">High Pressure Turbine</label></div>
                        <div><input type="radio" id="bsi_type_shilap" name="bsi_type" value="Shilap"><label for="bsi_type_shilap">Shilap</label></div>
                        <div><input type="radio" id="bsi_type_otra" name="bsi_type" value="Otra"><label for="bsi_type_otra">Otra</label></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="references_used">8. References Used <span class="asterisk">*</span></label>
                    <input type="text" id="references_used" name="references_used" required>
                </div>
            </fieldset>

            <fieldset>
                <legend>Aircraft Information</legend>
                <div class="form-group">
                    <p class="radio-group-label">9. Aircraft Model <span class="asterisk">*</span></p>
                    <div class="radio-group">
                        <div><input type="radio" id="model_a320neo" name="aircraft_model" value="A320 NEO" required><label for="model_a320neo">A320 NEO</label></div>
                        <div><input type="radio" id="model_a321neo" name="aircraft_model" value="A321 NEO"><label for="model_a321neo">A321 NEO</label></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="aircraft_registration">10. Aircraft Registration</label>
                    <select id="aircraft_registration" name="aircraft_registration">
                        <option value="" selected>Seleccione una matrícula (opcional)</option>
                        <option value="XA-VBA">XA-VBA</option>
                        <option value="XA-VBB">XA-VBB</option>
                        <option value="XA-VBH">XA-VBH</option>
                        <option value="XA-VBK">XA-VBK</option>
                        <option value="XA-VBM">XA-VBM</option>
                        <option value="XA-VBR">XA-VBR</option>
                        <option value="XA-VBS">XA-VBS</option>
                        <option value="XA-VBX">XA-VBX</option>
                        <option value="XA-VBY">XA-VBY</option>
                        <option value="XA-VBZ">XA-VBZ</option>
                        <option value="XA-VIA">XA-VIA</option>
                        <option value="XA-VIB">XA-VIB</option>
                        <option value="XA-VIE">XA-VIE</option>
                        <option value="XA-VIF">XA-VIF</option>
                        <option value="XA-VIH">XA-VIH</option>
                        <option value="XA-VII">XA-VII</option>
                        <option value="XA-VIJ">XA-VIJ</option>
                        <option value="XA-VIK">XA-VIK</option>
                        <option value="XA-VIL">XA-VIL</option>
                        <option value="XA-VIM">XA-VIM</option>
                        <option value="XA-VIN">XA-VIN</option>
                        <option value="XA-VIO">XA-VIO</option>
                        <option value="XA-VIP">XA-VIP</option>
                        <option value="XA-VIQ">XA-VIQ</option>
                        <option value="XA-VIR">XA-VIR</option>
                        <option value="XA-VIS">XA-VIS</option>
                        <option value="XA-VIT">XA-VIT</option>
                        <option value="XA-VIU">XA-VIU</option>
                        <option value="XA-VIV">XA-VIV</option>
                        <option value="XA-VIW">XA-VIW</option>
                        <option value="XA-VIX">XA-VIX</option>
                        <option value="XA-VIY">XA-VIY</option>
                        <option value="XA-VXA">XA-VXA</option>
                        <option value="XA-VXB">XA-VXB</option>
                        <option value="XA-VXC">XA-VXC</option>
                        <option value="XA-VXD">XA-VXD</option>
                        <option value="XA-VXE">XA-VXE</option>
                        <option value="XA-VXF">XA-VXF</option>
                        <option value="XA-VXG">XA-VXG</option>
                        <option value="XA-VXH">XA-VXH</option>
                        <option value="XA-VXI">XA-VXI</option>
                        <option value="XA-VXJ">XA-VXJ</option>
                        <option value="XA-VXK">XA-VXK</option>
                        <option value="XA-VXL">XA-VXL</option>
                        <option value="XA-VXM">XA-VXM</option>
                        <option value="XA-VXN">XA-VXN</option>
                        <option value="XA-VXO">XA-VXO</option>
                        <option value="XA-VXP">XA-VXP</option>
                        <option value="XA-VXQ">XA-VXQ</option>
                        <option value="XA-VXS">XA-VXS</option>
                        <option value="XA-VXT">XA-VXT</option>
                        <option value="XA-VXR">XA-VXR</option>
                        <option value="N/A">N/A</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="engine_sn">11. Engine S/N <span class="asterisk">*</span></label>
                    <input type="text" id="engine_sn" name="engine_sn" required>
                </div>
            </fieldset>

            <fieldset>
                <legend>Equipment Used</legend>
                <div class="form-group">
                    <p class="radio-group-label">12. Boroscope Used type <span class="asterisk">*</span></p>
                    <div class="radio-group">
                        <div><input type="radio" id="boro_mentor_iq" name="boroscope_type" value="Mentor IQ" required><label for="boro_mentor_iq">Mentor IQ</label></div>
                        <div><input type="radio" id="boro_olympus" name="boroscope_type" value="Olympus IV9000N"><label for="boro_olympus">Olympus IV9000N</label></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="boroscope_sn">13. Boroscope S/N <span class="asterisk">*</span></label>
                    <select id="boroscope_sn" name="boroscope_sn" required>
                        <option value="" disabled selected>Seleccione S/N Boroscopio</option>
                        <option value="1830A9916">1830A9916</option>
                        <option value="1541A9309">1541A9309</option>
                        <option value="1504A2839">1504A2839</option>
                        <option value="1852A8390">1852A8390</option>
                        <option value="2003A5682">2003A5682</option>
                        <option value="Y60005616.04">Y60005616.04</option>
                        <option value="Y00248620.11">Y00248620.11</option>
                        <option value="2315A5586">2315A5586</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="probe_sn">14. Probe S/N (If Apply) <span class="asterisk">*</span></label>
                    <select id="probe_sn" name="probe_sn" required>
                        <option value="" disabled selected>Seleccione S/N Sonda</option>
                        <option value="1602A1890">1602A1890</option>
                        <option value="1709A6840">1709A6840</option>
                        <option value="2220A0870">2220A0870</option>
                        <option value="2043A3156">2043A3156</option>
                        <option value="2130A9756">2130A9756</option>
                        <option value="2306A2954">2306A2954</option>
                        <option value="1506A3114">1506A3114</option>
                        <option value="1731A3106">1731A3106</option>
                        <option value="2003A5724">2003A5724</option>
                        <option value="2042A2970">2042A2970</option>
                        <option value="1639A3320">1639A3320</option>
                        <option value="2222A1410">2222A1410</option>
                        <option value="1832A0514">1832A0514</option>
                        <option value="2004A5804">2004A5804</option>
                        <option value="Y00319820.10">Y00319820.10</option>
                        <option value="Y60000716.03">Y60000716.03</option>
                    </select>
                </div>
            </fieldset>

            <!-- SECCIONES DE COMPONENTES (LPC, #3 Bearing, HPC, Combustion, SHIP LAP, HPT, LPT) -->
            <!-- Estructura: fieldset > legend > .form-group > (p.radio-group-label + div.radio-group) + (label + textarea) -->

            <fieldset>
                <legend>Low Compressor Section</legend>
                <div class="form-group">
                    <p class="radio-group-label">15. LPC STAGE 1</p>
                    <div class="radio-group">
                        <div><input type="radio" id="lpc_stage1_no_damage" name="lpc_stage1_status" value="No Damage Found"><label for="lpc_stage1_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="lpc_stage1_damage_in_limits" name="lpc_stage1_status" value="Damage In Limits"><label for="lpc_stage1_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="lpc_stage1_damage_out_limits" name="lpc_stage1_status" value="Damages Out of Limits"><label for="lpc_stage1_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="lpc_stage1_remarks" style="margin-top:15px;">16. LPC 1 Finding / Remarks</label>
                    <textarea id="lpc_stage1_remarks" name="lpc_stage1_remarks" rows="3"></textarea>
                    
                    <label for="lpc_stage1_images" style="margin-top:15px;">📷 Imágenes LPC Stage 1</label>
                    <input type="file" id="lpc_stage1_images" name="lpc_stage1_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="lpc_stage1">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="lpc_stage1_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">17. LPC STAGE 2</p>
                    <div class="radio-group">
                        <div><input type="radio" id="lpc_stage2_no_damage" name="lpc_stage2_status" value="No Damage Found"><label for="lpc_stage2_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="lpc_stage2_damage_in_limits" name="lpc_stage2_status" value="Damage In Limits"><label for="lpc_stage2_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="lpc_stage2_damage_out_limits" name="lpc_stage2_status" value="Damages Out of Limits"><label for="lpc_stage2_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="lpc_stage2_remarks" style="margin-top:15px;">18. LPC 2 Finding / Remarks</label>
                    <textarea id="lpc_stage2_remarks" name="lpc_stage2_remarks" rows="3"></textarea>
                    
                    <label for="lpc_stage2_images" style="margin-top:15px;">📷 Imágenes LPC Stage 2</label>
                    <input type="file" id="lpc_stage2_images" name="lpc_stage2_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="lpc_stage2">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="lpc_stage2_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">19. LPC STAGE 3</p>
                    <div class="radio-group">
                        <div><input type="radio" id="lpc_stage3_no_damage" name="lpc_stage3_status" value="No Damage Found"><label for="lpc_stage3_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="lpc_stage3_damage_in_limits" name="lpc_stage3_status" value="Damage In Limits"><label for="lpc_stage3_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="lpc_stage3_damage_out_limits" name="lpc_stage3_status" value="Damages Out of Limits"><label for="lpc_stage3_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="lpc_stage3_remarks" style="margin-top:15px;">20. LPC 3 Finding / Remarks</label>
                    <textarea id="lpc_stage3_remarks" name="lpc_stage3_remarks" rows="3"></textarea>
                    
                    <label for="lpc_stage3_images" style="margin-top:15px;">📷 Imágenes LPC Stage 3</label>
                    <input type="file" id="lpc_stage3_images" name="lpc_stage3_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="lpc_stage3">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="lpc_stage3_preview" class="section-image-preview"></div>
                </div>
            </fieldset>

            <fieldset>
                <legend># 3 Bearing</legend>
                <div class="form-group">
                    <p class="radio-group-label">21. # 3 BEARING FRONT SEAL</p>
                    <div class="radio-group">
                        <div><input type="radio" id="bearing3_front_no_damage" name="bearing3_front_status" value="No Damage Found"><label for="bearing3_front_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="bearing3_front_damage_in_limits" name="bearing3_front_status" value="Damage In Limits"><label for="bearing3_front_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="bearing3_front_damage_out_limits" name="bearing3_front_status" value="Damages Out of Limits"><label for="bearing3_front_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="bearing3_front_remarks" style="margin-top:15px;">22. # 3 Bearing front Seal Finding / Remarks</label>
                    <textarea id="bearing3_front_remarks" name="bearing3_front_remarks" rows="3"></textarea>
                    
                    <label for="bearing3_front_images" style="margin-top:15px;">📷 Imágenes # 3 Bearing Front Seal</label>
                    <input type="file" id="bearing3_front_images" name="bearing3_front_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="bearing3_front">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="bearing3_front_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">23. # 3 BEARING REAR SEAL</p>
                    <div class="radio-group">
                        <div><input type="radio" id="bearing3_rear_no_damage" name="bearing3_rear_status" value="No Damage Found"><label for="bearing3_rear_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="bearing3_rear_damage_in_limits" name="bearing3_rear_status" value="Damage In Limits"><label for="bearing3_rear_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="bearing3_rear_damage_out_limits" name="bearing3_rear_status" value="Damages Out of Limits"><label for="bearing3_rear_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="bearing3_rear_remarks" style="margin-top:15px;">24. # 3 Bearing rear Seal Finding / Remarks</label>
                    <textarea id="bearing3_rear_remarks" name="bearing3_rear_remarks" rows="3"></textarea>
                    
                    <label for="bearing3_rear_images" style="margin-top:15px;">📷 Imágenes # 3 Bearing Rear Seal</label>
                    <input type="file" id="bearing3_rear_images" name="bearing3_rear_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="bearing3_rear">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="bearing3_rear_preview" class="section-image-preview"></div>
                </div>
            </fieldset>

            <fieldset>
                <legend>High Compressor Section</legend>
                <!-- Repetir .form-group para HPC STAGE 1 a 8 -->
                <div class="form-group">
                    <p class="radio-group-label">25. HPC STAGE 1</p>
                    <div class="radio-group">
                        <div><input type="radio" id="hpc_stage1_no_damage" name="hpc_stage1_status" value="No Damage Found"><label for="hpc_stage1_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="hpc_stage1_damage_in_limits" name="hpc_stage1_status" value="Damage In Limits"><label for="hpc_stage1_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="hpc_stage1_damage_out_limits" name="hpc_stage1_status" value="Damages Out of Limits"><label for="hpc_stage1_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="hpc_stage1_remarks" style="margin-top:15px;">26. HPC Stage 1 Finding / Remarks</label>
                    <textarea id="hpc_stage1_remarks" name="hpc_stage1_remarks" rows="3"></textarea>
                    
                    <label for="hpc_stage1_images" style="margin-top:15px;">📷 Imágenes HPC Stage 1</label>
                    <input type="file" id="hpc_stage1_images" name="hpc_stage1_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="hpc_stage1">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="hpc_stage1_preview" class="section-image-preview"></div>
                </div>
                <!-- ... (HPC Stages 2-8) ... -->
                 <div class="form-group">
                    <p class="radio-group-label">27. HPC STAGE 2</p>
                    <div class="radio-group">
                        <div><input type="radio" id="hpc_stage2_no_damage" name="hpc_stage2_status" value="No Damage Found"><label for="hpc_stage2_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="hpc_stage2_damage_in_limits" name="hpc_stage2_status" value="Damage In Limits"><label for="hpc_stage2_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="hpc_stage2_damage_out_limits" name="hpc_stage2_status" value="Damages Out of Limits"><label for="hpc_stage2_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="hpc_stage2_remarks" style="margin-top:15px;">28. HPC Stage 2 Finding / Remarks</label>
                    <textarea id="hpc_stage2_remarks" name="hpc_stage2_remarks" rows="3"></textarea>
                    
                    <label for="hpc_stage2_images" style="margin-top:15px;">📷 Imágenes HPC Stage 2</label>
                    <input type="file" id="hpc_stage2_images" name="hpc_stage2_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="hpc_stage2">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="hpc_stage2_preview" class="section-image-preview"></div>
                </div>
                 <div class="form-group">
                    <p class="radio-group-label">29. HPC STAGE 3</p>
                    <div class="radio-group">
                        <div><input type="radio" id="hpc_stage3_no_damage" name="hpc_stage3_status" value="No Damage Found"><label for="hpc_stage3_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="hpc_stage3_damage_in_limits" name="hpc_stage3_status" value="Damage In Limits"><label for="hpc_stage3_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="hpc_stage3_damage_out_limits" name="hpc_stage3_status" value="Damages Out of Limits"><label for="hpc_stage3_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="hpc_stage3_remarks" style="margin-top:15px;">30. HPC Stage 3 Finding / Remarks</label>
                    <textarea id="hpc_stage3_remarks" name="hpc_stage3_remarks" rows="3"></textarea>
                    
                    <label for="hpc_stage3_images" style="margin-top:15px;">📷 Imágenes HPC Stage 3</label>
                    <input type="file" id="hpc_stage3_images" name="hpc_stage3_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="hpc_stage3">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="hpc_stage3_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">31. HPC STAGE 4</p>
                    <div class="radio-group">
                        <div><input type="radio" id="hpc_stage4_no_damage" name="hpc_stage4_status" value="No Damage Found"><label for="hpc_stage4_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="hpc_stage4_damage_in_limits" name="hpc_stage4_status" value="Damage In Limits"><label for="hpc_stage4_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="hpc_stage4_damage_out_limits" name="hpc_stage4_status" value="Damages Out of Limits"><label for="hpc_stage4_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="hpc_stage4_remarks" style="margin-top:15px;">32. HPC Stage 4 Finding / Remarks</label>
                    <textarea id="hpc_stage4_remarks" name="hpc_stage4_remarks" rows="3"></textarea>
                    
                    <label for="hpc_stage4_images" style="margin-top:15px;">📷 Imágenes HPC Stage 4</label>
                    <input type="file" id="hpc_stage4_images" name="hpc_stage4_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="hpc_stage4">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="hpc_stage4_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">33. HPC STAGE 5</p>
                    <div class="radio-group">
                        <div><input type="radio" id="hpc_stage5_no_damage" name="hpc_stage5_status" value="No Damage Found"><label for="hpc_stage5_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="hpc_stage5_damage_in_limits" name="hpc_stage5_status" value="Damage In Limits"><label for="hpc_stage5_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="hpc_stage5_damage_out_limits" name="hpc_stage5_status" value="Damages Out of Limits"><label for="hpc_stage5_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="hpc_stage5_remarks" style="margin-top:15px;">34. HPC Stage 5 Finding / Remarks</label>
                    <textarea id="hpc_stage5_remarks" name="hpc_stage5_remarks" rows="3"></textarea>
                    
                    <label for="hpc_stage5_images" style="margin-top:15px;">📷 Imágenes HPC Stage 5</label>
                    <input type="file" id="hpc_stage5_images" name="hpc_stage5_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="hpc_stage5">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="hpc_stage5_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">35. HPC STAGE 6</p>
                    <div class="radio-group">
                        <div><input type="radio" id="hpc_stage6_no_damage" name="hpc_stage6_status" value="No Damage Found"><label for="hpc_stage6_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="hpc_stage6_damage_in_limits" name="hpc_stage6_status" value="Damage In Limits"><label for="hpc_stage6_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="hpc_stage6_damage_out_limits" name="hpc_stage6_status" value="Damages Out of Limits"><label for="hpc_stage6_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="hpc_stage6_remarks" style="margin-top:15px;">36. HPC Stage 6 Finding / Remarks</label>
                    <textarea id="hpc_stage6_remarks" name="hpc_stage6_remarks" rows="3"></textarea>
                    
                    <label for="hpc_stage6_images" style="margin-top:15px;">📷 Imágenes HPC Stage 6</label>
                    <input type="file" id="hpc_stage6_images" name="hpc_stage6_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="hpc_stage6">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="hpc_stage6_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">37. HPC STAGE 7</p>
                    <div class="radio-group">
                        <div><input type="radio" id="hpc_stage7_no_damage" name="hpc_stage7_status" value="No Damage Found"><label for="hpc_stage7_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="hpc_stage7_damage_in_limits" name="hpc_stage7_status" value="Damage In Limits"><label for="hpc_stage7_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="hpc_stage7_damage_out_limits" name="hpc_stage7_status" value="Damages Out of Limits"><label for="hpc_stage7_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="hpc_stage7_remarks" style="margin-top:15px;">38. HPC Stage 7 Finding / Remarks</label>
                    <textarea id="hpc_stage7_remarks" name="hpc_stage7_remarks" rows="3"></textarea>
                    
                    <label for="hpc_stage7_images" style="margin-top:15px;">📷 Imágenes HPC Stage 7</label>
                    <input type="file" id="hpc_stage7_images" name="hpc_stage7_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="hpc_stage7">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="hpc_stage7_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">39. HPC STAGE 8</p>
                    <div class="radio-group">
                        <div><input type="radio" id="hpc_stage8_no_damage" name="hpc_stage8_status" value="No Damage Found"><label for="hpc_stage8_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="hpc_stage8_damage_in_limits" name="hpc_stage8_status" value="Damage In Limits"><label for="hpc_stage8_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="hpc_stage8_damage_out_limits" name="hpc_stage8_status" value="Damages Out of Limits"><label for="hpc_stage8_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="hpc_stage8_remarks" style="margin-top:15px;">40. HPC Stage 8 Finding / Remarks</label>
                    <textarea id="hpc_stage8_remarks" name="hpc_stage8_remarks" rows="3"></textarea>
                    
                    <label for="hpc_stage8_images" style="margin-top:15px;">📷 Imágenes HPC Stage 8</label>
                    <input type="file" id="hpc_stage8_images" name="hpc_stage8_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="hpc_stage8">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="hpc_stage8_preview" class="section-image-preview"></div>
                </div>
            </fieldset>

            <fieldset>
                <legend>Combustion Chamber</legend>
                <div class="form-group">
                    <p class="radio-group-label">41. IGNITER SEGMENT</p>
                    <div class="radio-group">
                        <div><input type="radio" id="igniter_no_damage" name="igniter_status" value="No Damage Found"><label for="igniter_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="igniter_damage_in_limits" name="igniter_status" value="Damage In Limits"><label for="igniter_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="igniter_damage_out_limits" name="igniter_status" value="Damages Out of Limits"><label for="igniter_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="igniter_remarks" style="margin-top:15px;">42. Igniter Segment Finding / Remarks</label>
                    <textarea id="igniter_remarks" name="igniter_remarks" rows="3"></textarea>
                    
                    <label for="igniter_images" style="margin-top:15px;">📷 Imágenes Igniter Segment</label>
                    <input type="file" id="igniter_images" name="igniter_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="igniter">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="igniter_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">43. FUEL NOZZLE</p>
                    <div class="radio-group">
                        <div><input type="radio" id="fuelnozzle_no_damage" name="fuelnozzle_status" value="No Damage Found"><label for="fuelnozzle_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="fuelnozzle_damage_in_limits" name="fuelnozzle_status" value="Damage In Limits"><label for="fuelnozzle_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="fuelnozzle_damage_out_limits" name="fuelnozzle_status" value="Damages Out of Limits"><label for="fuelnozzle_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="fuelnozzle_remarks" style="margin-top:15px;">44. Fuel Nozzle Finding / Remarks</label>
                    <textarea id="fuelnozzle_remarks" name="fuelnozzle_remarks" rows="3"></textarea>
                    
                    <label for="fuelnozzle_images" style="margin-top:15px;">📷 Imágenes Fuel Nozzle</label>
                    <input type="file" id="fuelnozzle_images" name="fuelnozzle_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="fuelnozzle">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="fuelnozzle_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">45. CCH INNER LINER</p>
                    <div class="radio-group">
                        <div><input type="radio" id="cch_inner_no_damage" name="cch_inner_status" value="No Damage Found"><label for="cch_inner_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="cch_inner_damage_in_limits" name="cch_inner_status" value="Damage In Limits"><label for="cch_inner_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="cch_inner_damage_out_limits" name="cch_inner_status" value="Damages Out of Limits"><label for="cch_inner_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="cch_inner_remarks" style="margin-top:15px;">46. CCH Inner Liner Finding / Remarks</label>
                    <textarea id="cch_inner_remarks" name="cch_inner_remarks" rows="3"></textarea>
                    
                    <label for="cch_inner_images" style="margin-top:15px;">📷 Imágenes CCH Inner Liner</label>
                    <input type="file" id="cch_inner_images" name="cch_inner_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="cch_inner">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="cch_inner_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">47. CCH OUTER LINER</p>
                    <div class="radio-group">
                        <div><input type="radio" id="cch_outer_no_damage" name="cch_outer_status" value="No Damage Found"><label for="cch_outer_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="cch_outer_damage_in_limits" name="cch_outer_status" value="Damage In Limits"><label for="cch_outer_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="cch_outer_damage_out_limits" name="cch_outer_status" value="Damages Out of Limits"><label for="cch_outer_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="cch_outer_remarks" style="margin-top:15px;">48. CCH Outer Liner Finding / Remarks</label>
                    <textarea id="cch_outer_remarks" name="cch_outer_remarks" rows="3"></textarea>
                    
                    <label for="cch_outer_images" style="margin-top:15px;">📷 Imágenes CCH Outer Liner</label>
                    <input type="file" id="cch_outer_images" name="cch_outer_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="cch_outer">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="cch_outer_preview" class="section-image-preview"></div>
                </div>
            </fieldset>

            <fieldset>
                <legend>SHIP LAP</legend>
                <div class="form-group">
                    <p class="radio-group-label">49. SHIPLAP</p>
                    <div class="radio-group">
                        <div><input type="radio" id="shiplap_no_damage" name="shiplap_status" value="No Damage Found"><label for="shiplap_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="shiplap_damage_in_limits" name="shiplap_status" value="Damage In Limits"><label for="shiplap_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="shiplap_damage_out_limits" name="shiplap_status" value="Damages Out of Limits"><label for="shiplap_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="shiplap_remarks" style="margin-top:15px;">50. Shiplap Finding / Remarks</label>
                    <textarea id="shiplap_remarks" name="shiplap_remarks" rows="3"></textarea>
                    
                    <label for="shiplap_images" style="margin-top:15px;">📷 Imágenes Shiplap</label>
                    <input type="file" id="shiplap_images" name="shiplap_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="shiplap">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="shiplap_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <label for="shiplap_dimensions">51. Ship Lap Dimensions</label>
                    <input type="text" id="shiplap_dimensions" name="shiplap_dimensions">
                </div>
            </fieldset>

            <fieldset>
                <legend>High Pressure Turbine</legend>
                <div class="form-group">
                    <p class="radio-group-label">52. HPT VANE</p>
                    <div class="radio-group">
                        <div><input type="radio" id="hpt_vane_no_damage" name="hpt_vane_status" value="No Damage Found"><label for="hpt_vane_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="hpt_vane_damage_in_limits" name="hpt_vane_status" value="Damage In Limits"><label for="hpt_vane_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="hpt_vane_damage_out_limits" name="hpt_vane_status" value="Damages Out of Limits"><label for="hpt_vane_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="hpt_vane_remarks" style="margin-top:15px;">53. HPT VANE Finding / Remarks</label>
                    <textarea id="hpt_vane_remarks" name="hpt_vane_remarks" rows="3"></textarea>
                    
                    <label for="hpt_vane_images" style="margin-top:15px;">📷 Imágenes HPT Vane</label>
                    <input type="file" id="hpt_vane_images" name="hpt_vane_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="hpt_vane">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="hpt_vane_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">54. HPT STAGE 1</p>
                    <div class="radio-group">
                        <div><input type="radio" id="hpt_s1_no_damage" name="hpt_s1_status" value="No Damage Found"><label for="hpt_s1_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="hpt_s1_damage_in_limits" name="hpt_s1_status" value="Damage In Limits"><label for="hpt_s1_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="hpt_s1_damage_out_limits" name="hpt_s1_status" value="Damages Out of Limits"><label for="hpt_s1_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="hpt_s1_remarks" style="margin-top:15px;">55. HPT Stage 1 Finding / Remarks</label>
                    <textarea id="hpt_s1_remarks" name="hpt_s1_remarks" rows="3"></textarea>
                    
                    <label for="hpt_s1_images" style="margin-top:15px;">📷 Imágenes HPT Stage 1</label>
                    <input type="file" id="hpt_s1_images" name="hpt_s1_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="hpt_s1">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="hpt_s1_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">56. HPT STAGE 2</p>
                    <div class="radio-group">
                        <div><input type="radio" id="hpt_s2_no_damage" name="hpt_s2_status" value="No Damage Found"><label for="hpt_s2_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="hpt_s2_damage_in_limits" name="hpt_s2_status" value="Damage In Limits"><label for="hpt_s2_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="hpt_s2_damage_out_limits" name="hpt_s2_status" value="Damages Out of Limits"><label for="hpt_s2_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="hpt_s2_remarks" style="margin-top:15px;">57. HPT Stage 2 Finding / Remarks</label>
                    <textarea id="hpt_s2_remarks" name="hpt_s2_remarks" rows="3"></textarea>
                    
                    <label for="hpt_s2_images" style="margin-top:15px;">📷 Imágenes HPT Stage 2</label>
                    <input type="file" id="hpt_s2_images" name="hpt_s2_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="hpt_s2">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="hpt_s2_preview" class="section-image-preview"></div>
                </div>
            </fieldset>

            <fieldset>
                <legend>LOW PRESSURE TURBINE</legend>
                <div class="form-group">
                    <p class="radio-group-label">58. LPT STAGE 1</p>
                    <div class="radio-group">
                        <div><input type="radio" id="lpt_s1_no_damage" name="lpt_s1_status" value="No Damage Found"><label for="lpt_s1_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="lpt_s1_damage_in_limits" name="lpt_s1_status" value="Damage In Limits"><label for="lpt_s1_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="lpt_s1_damage_out_limits" name="lpt_s1_status" value="Damages Out of Limits"><label for="lpt_s1_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="lpt_s1_remarks" style="margin-top:15px;">59. LPT Stage 1 Finding / Remarks</label>
                    <textarea id="lpt_s1_remarks" name="lpt_s1_remarks" rows="3"></textarea>
                    
                    <label for="lpt_s1_images" style="margin-top:15px;">📷 Imágenes LPT Stage 1</label>
                    <input type="file" id="lpt_s1_images" name="lpt_s1_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="lpt_s1">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="lpt_s1_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">60. LPT STAGE 2</p>
                    <div class="radio-group">
                        <div><input type="radio" id="lpt_s2_no_damage" name="lpt_s2_status" value="No Damage Found"><label for="lpt_s2_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="lpt_s2_damage_in_limits" name="lpt_s2_status" value="Damage In Limits"><label for="lpt_s2_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="lpt_s2_damage_out_limits" name="lpt_s2_status" value="Damages Out of Limits"><label for="lpt_s2_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="lpt_s2_remarks" style="margin-top:15px;">61. LPT Stage 2 Finding / Remarks</label>
                    <textarea id="lpt_s2_remarks" name="lpt_s2_remarks" rows="3"></textarea>
                    
                    <label for="lpt_s2_images" style="margin-top:15px;">📷 Imágenes LPT Stage 2</label>
                    <input type="file" id="lpt_s2_images" name="lpt_s2_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="lpt_s2">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="lpt_s2_preview" class="section-image-preview"></div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">62. LPT STAGE 3</p>
                    <div class="radio-group">
                        <div><input type="radio" id="lpt_s3_no_damage" name="lpt_s3_status" value="No Damage Found"><label for="lpt_s3_no_damage">No Damage Found</label></div>
                        <div><input type="radio" id="lpt_s3_damage_in_limits" name="lpt_s3_status" value="Damage In Limits"><label for="lpt_s3_damage_in_limits">Damage In Limits</label></div>
                        <div><input type="radio" id="lpt_s3_damage_out_limits" name="lpt_s3_status" value="Damages Out of Limits"><label for="lpt_s3_damage_out_limits">Damages Out of Limits</label></div>
                    </div>
                    <label for="lpt_s3_remarks" style="margin-top:15px;">63. LPT Stage 3 Finding / Remarks</label>
                    <textarea id="lpt_s3_remarks" name="lpt_s3_remarks" rows="3"></textarea>
                    
                    <label for="lpt_s3_images" style="margin-top:15px;">📷 Imágenes LPT Stage 3</label>
                    <input type="file" id="lpt_s3_images" name="lpt_s3_images" 
                           multiple accept="image/*" class="section-image-upload" data-section="lpt_s3">
                    <p class="form-note" style="font-size: 0.8em; margin: 5px 0;">📁 Formatos: JPG, PNG, GIF. Máx: 10MB por imagen.</p>
                    <div id="lpt_s3_preview" class="section-image-preview"></div>
                </div>
            </fieldset>

            <fieldset>
                <legend>Final Disposition</legend>
                <div class="form-note">64. Coloque disposiciones finales de manera resumida</div>
                <div class="form-note">65. Defina el estado del motor luego de la inspección con las opciones dadas</div>
                <div class="form-note">66. Indique el nuevo intervalo, el mas restrictivo, FC o FH, solo ponga uno, si no hay nuevos intervalos coloque el actual.</div>
                <div class="form-note">67. Coloque su correo electrónico</div>
                <div class="form-note">68. Coloque el tiempo invertido en minutos.</div>

                <div class="form-group">
                    <label for="final_disposition">64. Final disposition, Restrictions or New Restrictions <span class="asterisk">*</span></label>
                    <textarea id="final_disposition" name="final_disposition" required rows="4"></textarea>
                </div>

                <div class="form-group">
                    <p class="radio-group-label">65. Engine Status after BSI <span class="asterisk">*</span></p>
                    <div class="radio-group">
                        <div><input type="radio" id="engine_status_serviceable" name="engine_status_bsi" value="Serviceable to Operation" required><label for="engine_status_serviceable">Serviceable to Operation</label></div>
                        <div><input type="radio" id="engine_status_remove_30fc" name="engine_status_bsi" value="Remove before 30 FC"><label for="engine_status_remove_30fc">Remove before 30 FC</label></div>
                        <div><input type="radio" id="engine_status_remove_10fc" name="engine_status_bsi" value="Remove before 10 FC"><label for="engine_status_remove_10fc">Remove before 10 FC</label></div>
                        <div><input type="radio" id="engine_status_remove_5fc" name="engine_status_bsi" value="Remove before 5 FC"><label for="engine_status_remove_5fc">Remove before 5 FC</label></div>
                        <div><input type="radio" id="engine_status_remove_next_flight" name="engine_status_bsi" value="Shall be Removed before next flight"><label for="engine_status_remove_next_flight">Shall be Removed before next flight</label></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="new_interval_inspections">66. New Interval Inspections (If Apply) <span class="asterisk">*</span></label>
                    <input type="text" id="new_interval_inspections" name="new_interval_inspections" required>
                </div>

                <div class="form-group">
                    <label for="user_email">67. Put your email <span class="asterisk">*</span></label>
                    <input type="email" id="user_email" name="user_email" required>
                </div>

                <div class="form-group">
                    <label for="inspection_time">68. Inspection Time (Minutes) <span class="asterisk">*</span></label>
                    <input type="number" id="inspection_time" name="inspection_time" min="1" required placeholder="Ej: 60">
                    <p class="form-note" style="font-size: 0.8em; margin-top: 5px;">Escriba un número mayor que 0.</p>
                </div>
            </fieldset>

            <fieldset>
                <legend>Intervals (for Statistics)</legend>
                <p class="form-note">In this section indicate if inspection was or not affected, if the answer is YES, put new interval, FH & FC. If interval was not affected put the actual intervals in FH & FC</p>

                <div class="form-group">
                    <p class="radio-group-label">69. Interval inspection Affected? <span class="asterisk">*</span></p>
                    <div class="radio-group">
                        <div><input type="radio" id="interval_affected_yes" name="interval_affected" value="Yes" required><label for="interval_affected_yes">Yes</label></div>
                        <div><input type="radio" id="interval_affected_no" name="interval_affected" value="No"><label for="interval_affected_no">No</label></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="interval_next_fc">70. Interval for next Inspection (FC)</label>
                    <input type="text" id="interval_next_fc" name="interval_next_fc">
                </div>

                <div class="form-group">
                    <label for="interval_next_fh">71. Interval for next Inspection (FH)</label>
                    <input type="text" id="interval_next_fh" name="interval_next_fh">
                </div>
            </fieldset>



            <div class="button-group">
                <div class="button-row">
                    <button type="button" class="action-button secondary clear-form-btn">🗑️ Limpiar Formulario</button>
                    <button type="button" class="action-button warning debug-fill-btn">Llenar Formulario (Debug)</button>
                </div>
                <div class="button-row">
                    <button type="button" class="action-button primary generate-word-btn">📄 Generar Documento Word</button>
                    <button type="button" id="previewDocBtn" class="action-button primary preview-doc-btn">Previsualizar Documento</button>
                </div>
            </div>
        </form>
    </div>

    <!-- Modal para previsualización profesional -->
    <div id="previewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Previsualización del Documento BSI PW1100</h2>
                <span class="close-button">&times;</span>
            </div>
            <div class="modal-body">
                <button class="floating-close-btn" type="button">&times;</button>
                <div id="previewPagesContainer">
                    <!-- Las páginas de previsualización se cargarán aquí -->
                </div>
            </div>
            <div class="page-navigation">
                <button id="prevPageBtn">Anterior</button>
                <span id="pageNumberDisplay">Página 1 de 5</span>
                <button id="nextPageBtn">Siguiente</button>
                <button id="downloadPdfBtn" class="download-pdf-btn">📄 Descargar como PDF</button>
            </div>
        </div>
    </div>

    <style>
        .modal {
            display: none; /* Hidden by default */
            position: fixed; /* Stay in place */
            z-index: 1000; /* Sit on top */
            left: 0;
            top: 0;
            width: 100%; /* Full width */
            height: 100%; /* Full height */
            overflow: auto; /* Enable scroll if needed */
            background-color: rgba(0,0,0,0.7); /* Black w/ opacity */
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: #fefefe;
            margin: auto;
            padding: 20px;
            border: 1px solid #888;
            width: 90%; /* Could be responsive */
            max-width: 900px; /* Max width for larger screens */
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2), 0 6px 20px 0 rgba(0,0,0,0.19);
            animation-name: animatetop;
            animation-duration: 0.4s;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .close-button {
            color: #aaa;
            position: absolute;
            right: 20px;
            top: 10px;
            font-size: 28px;
            font-weight: bold;
        }

        .close-button:hover,
        .close-button:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        .pdf-container {
            flex-grow: 1;
            height: 70vh; /* Adjust height as needed */
            padding-top: 10px;
        }

        .pdf-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* Add Animation */
        @-webkit-keyframes animatetop {
            from {top:-300px; opacity:0} 
            to {top:0; opacity:1}
        }

        @keyframes animatetop {
            from {top:-300px; opacity:0} 
            to {top:0; opacity:1}
        }

        /* Estilos para el botón de descarga PDF */
        .download-pdf-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            margin-left: 20px;
            transition: background 0.3s ease;
        }

        .download-pdf-btn:hover {
            background: #c0392b;
        }

        .download-pdf-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        /* Estilos para manejar texto largo en previsualización */
        .modal-body {
            word-wrap: break-word;
            overflow-wrap: break-word;
            word-break: break-all;
        }

        #previewPagesContainer {
            word-wrap: break-word;
            overflow-wrap: break-word;
            overflow-x: auto;
            max-width: 100%;
        }

        /* Estilos específicos para elementos de texto en previsualización */
        .modal-body textarea,
        .modal-body input[type="text"],
        .modal-body .text-content,
        .modal-body td,
        .modal-body p {
            word-wrap: break-word;
            overflow-wrap: break-word;
            word-break: break-word;
            max-width: 100%;
            white-space: pre-wrap;
        }

        /* Estilos para la navegación de páginas */
        .page-navigation {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-top: 1px solid #ddd;
        }

        .page-navigation button {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
        }

        .page-navigation button:hover:not(:disabled) {
            background: #2980b9;
        }

        .page-navigation button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        /* Estilos para inputs de imagen por sección */
        .section-image-upload {
            width: 100%;
            padding: 8px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            background: #f9f9f9;
            margin: 5px 0;
            transition: border-color 0.3s ease;
        }

        .section-image-upload:hover {
            border-color: #3498db;
            background: #f0f8ff;
        }

        .section-image-preview {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #e9ecef;
            min-height: 50px;
            display: none; /* Hidden hasta que haya imágenes */
        }

        .section-image-preview.has-images {
            display: block;
        }

        .section-image-item {
            display: inline-block;
            margin: 5px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            max-width: 200px;
            text-align: center;
        }

        .section-image-item img {
            max-width: 100%;
            max-height: 100px;
            border-radius: 3px;
            margin-bottom: 5px;
        }

        .section-image-item .image-name {
            font-size: 0.8em;
            color: #666;
            margin-bottom: 5px;
            word-break: break-all;
        }

        .section-image-item .remove-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 3px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.7em;
        }

        .section-image-item .remove-btn:hover {
            background: #c0392b;
        }

        /* Estilos para indicar las secciones con imágenes */
        .form-group.has-section-images {
            border-left: 4px solid #27ae60;
            padding-left: 15px;
        }

        .form-group.has-section-images::before {
            content: "📷";
            position: absolute;
            left: -10px;
            background: #27ae60;
            color: white;
            padding: 2px 5px;
            border-radius: 3px;
            font-size: 0.8em;
        }
    </style>

</body>
</html>