require('dotenv').config();
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const fs = require('fs-extra');
const multer = require('multer');
const { promisify } = require('util');

// Importar los generadores de documentos
const BSI_PW1100_Generator = require('./js/generators/BSI_PW1100_Generator');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));

// Servir archivos estáticos
app.use(express.static('.'));

// Configuración de base de datos centralizada
const getDbConfig = () => ({
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: process.env.DB_PORT || 3306,
    connectTimeout: 10000
});

// Función para manejar conexiones de BD con fallback
async function executeDbQuery(queryFn) {
    try {
        const dbConfig = getDbConfig();
        
        if (!dbConfig.host || !dbConfig.user || !dbConfig.password) {
            return { success: true, message: 'BD no configurada', data: [] };
        }
        
        const mysql = require('mysql2/promise');
        const connection = await mysql.createConnection(dbConfig);
        
        const result = await queryFn(connection);
        await connection.end();
        
        return { success: true, data: result };
        
    } catch (error) {
        if (error.code === 'ETIMEDOUT' || error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('⚠️ BD no disponible, continuando sin BD...');
            return { success: true, message: 'BD no disponible', data: [] };
        }
        
        console.error('Error BD:', error.message);
        return { success: true, message: 'Error BD', data: [] };
    }
}

// Configurar multer para subida de imágenes
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = './uploads/images';
        fs.ensureDirSync(uploadDir);
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({ 
    storage: storage,
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('Solo se permiten archivos de imagen'), false);
        }
    },
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB máximo
    }
});

// Rutas principales
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Ruta para guardar formulario en RDS
app.post('/api/save-form', async (req, res) => {
    try {
        const result = await executeDbQuery(async (connection) => {
            const insertSQL = `
                INSERT INTO form_submissions 
                (id, work_order_number, form_type, form_data, pdf_s3_key, status) 
                VALUES (?, ?, ?, ?, ?, ?)
            `;
            
            return await connection.execute(insertSQL, [
                req.body.id,
                req.body.work_order_number,
                req.body.form_type,
                req.body.form_data,
                req.body.pdf_s3_key,
                req.body.status
            ]);
        });
        
        res.json({ success: true, message: 'Formulario guardado exitosamente' });
        
    } catch (error) {
        console.error('Error guardando formulario:', error);
        res.status(500).json({ error: 'Error guardando formulario', details: error.message });
    }
});

// Ruta para obtener todos los formularios enviados
app.get('/api/submitted-forms', async (req, res) => {
    try {
        const result = await executeDbQuery(async (connection) => {
            const selectSQL = `
                SELECT 
                    id, work_order_number, form_type, form_data,
                    pdf_s3_key, status, created_at
                FROM form_submissions 
                WHERE form_type = 'BSI_PW1100'
                ORDER BY created_at DESC
            `;
            
            const [rows] = await connection.execute(selectSQL);
            return rows;
        });
        
        if (!result.success) {
            return res.json({ success: true, forms: [], total: 0 });
        }
        
        const processedForms = result.data.map(row => {
            let formData = {};
            try {
                formData = JSON.parse(row.form_data);
            } catch (e) {
                console.warn('Error parsing form_data for ID:', row.id);
                formData = {};
            }
            
            return {
                id: row.id,
                work_order_number: row.work_order_number,
                form_type: row.form_type,
                pdf_s3_key: row.pdf_s3_key,
                status: row.status,
                created_at: row.created_at,
                aircraft_registration: formData.aircraft_registration || 'N/A',
                engine_sn: formData.engine_sn || 'N/A',
                inspected_by: formData.inspected_by || 'N/A',
                aircraft_model: formData.aircraft_model || 'N/A',
                date_of_bsi: formData.date_of_bsi || 'N/A'
            };
        });
        
        res.json({ 
            success: true, 
            forms: processedForms,
            total: processedForms.length
        });
        
    } catch (error) {
        console.error('Error consultando formularios enviados:', error);
        res.json({ success: true, forms: [], total: 0 });
    }
});

// Resto de rutas sin cambios...
app.post('/generate-bsi-pw1100', upload.array('images', 10), async (req, res) => {
    try {
        console.log('Datos recibidos:', req.body);
        console.log('Imágenes recibidas:', req.files?.length || 0);

        const imagesWithDescriptions = req.files ? req.files.map((file, index) => {
            const descriptionKey = `image_description_${index}`;
            return {
                ...file,
                description: req.body[descriptionKey] || `Imagen ${index + 1}`
            };
        }) : [];

        const generator = new BSI_PW1100_Generator();
        const documentBuffer = await generator.generateDocument(req.body, imagesWithDescriptions);

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
        res.setHeader('Content-Disposition', `attachment; filename="BSI_PW1100_Report_${Date.now()}.docx"`);
        
        res.send(documentBuffer);

        setTimeout(() => {
            if (req.files) {
                req.files.forEach(file => {
                    fs.remove(file.path).catch(console.error);
                });
            }
        }, 5000);

    } catch (error) {
        console.error('Error generando documento:', error);
        res.status(500).json({ 
            error: 'Error generando el documento', 
            details: error.message 
        });
    }
});

// ====================================================
// ENDPOINT PARA MANEJO DE IMÁGENES (FALLBACK CUANDO AWS NO ESTÁ DISPONIBLE)
// ====================================================

// Endpoint para subir imágenes (fallback local) - usa configuración de multer existente
app.post('/api/upload-image-s3', upload.single('image'), async (req, res) => {
    try {
        console.log('📸 Recibida solicitud de subida de imagen (modo local)');
        
        if (!req.file) {
            return res.status(400).json({ error: 'No se recibió archivo de imagen' });
        }
        
        const { mode = 'draft', formId } = req.body;
        
        // Generar key similar a S3
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const fileExtension = path.extname(req.file.originalname);
        const baseName = path.basename(req.file.originalname, fileExtension).replace(/[^a-zA-Z0-9]/g, '_');
        const s3Key = `images/draft/${timestamp}_${baseName}${fileExtension}`;
        
        // URL local para acceder al archivo
        const localUrl = `http://localhost:${PORT}/uploads/images/${req.file.filename}`;
        
        console.log(`✅ Imagen guardada localmente: ${req.file.filename}`);
        
        res.json({
            success: true,
            key: s3Key,
            url: localUrl,
            bucket: 'local-storage',
            uploadedAt: new Date().toISOString(),
            size: req.file.size,
            type: req.file.mimetype,
            message: 'Imagen guardada en almacenamiento local'
        });
        
    } catch (error) {
        console.error('❌ Error en subida de imagen:', error);
        res.status(500).json({ 
            error: 'Error procesando imagen',
            details: error.message 
        });
    }
});

// Servir imágenes subidas
app.use('/uploads', express.static('./uploads'));

// Iniciar servidor
app.listen(PORT, () => {
    console.log(`Servidor ejecutándose en http://localhost:${PORT}`);
    console.log('Generador de documentos Word para formularios Vivaaerobus');
    
    // Verificar configuración de BD
    const dbConfig = getDbConfig();
    if (!dbConfig.host || !dbConfig.user || !dbConfig.password) {
        console.log('⚠️ Base de datos no configurada - funcionando en modo offline');
    } else {
        console.log('✅ Configuración de BD cargada');
    }
    
    console.log('📸 Endpoint de imágenes configurado: /api/upload-image-s3');
    console.log('📁 Directorio de imágenes: ./uploads/images/');
});

module.exports = app;