/**
 * ====================================================
 * GESTOR DE FORMULARIOS ENVIADOS
 * Sistema completo para consultar, mostrar y editar formularios BSI PW1100 enviados
 * ====================================================
 */

class SubmittedFormsManager {
    constructor() {
        this.submittedForms = [];
        this.currentSection = 'available';
        this.isLoading = false;
        
        this.init();
    }

    init() {
        console.log('🚀 Iniciando sistema de formularios enviados...');
        
        // Esperar a que el DOM esté completamente cargado
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupEventListeners();
                this.loadSubmittedForms();
            });
        } else {
            this.setupEventListeners();
            this.loadSubmittedForms();
        }
    }

    setupEventListeners() {
        console.log('🔧 Configurando event listeners...');
        
        // Event listeners para pestañas
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                this.switchSection(section);
            });
        });

        // Event listener para búsqueda de formularios enviados
        const searchInput = document.getElementById('search-submitted');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterSubmittedForms(e.target.value);
            });
        }

        console.log('✅ Event listeners configurados');
    }

    switchSection(section) {
        console.log(`🔄 Cambiando a sección: ${section}`);
        
        // Actualizar estado de pestañas
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.section === section) {
                btn.classList.add('active');
            }
        });

        // Mostrar/ocultar contenido de secciones
        const availableSection = document.getElementById('available-forms');
        const submittedSection = document.getElementById('submitted-forms');

        if (section === 'available') {
            availableSection.classList.remove('hidden');
            submittedSection.classList.add('hidden');
        } else if (section === 'submitted') {
            availableSection.classList.add('hidden');
            submittedSection.classList.remove('hidden');
            
            // Cargar formularios enviados si es la primera vez
            if (this.submittedForms.length === 0 && !this.isLoading) {
                this.loadSubmittedForms();
            }
        }

        this.currentSection = section;
    }

    async loadSubmittedForms() {
        console.log('📥 Cargando formularios enviados...');
        
        if (this.isLoading) {
            console.log('⏳ Ya hay una carga en progreso');
            return;
        }

        this.isLoading = true;
        this.showLoadingState();

        try {
            const response = await fetch('/api/submitted-forms');
            const data = await response.json();

            if (data.success) {
                this.submittedForms = data.forms;
                console.log(`✅ Cargados ${data.forms.length} formularios enviados`);
                
                this.renderSubmittedForms();
            } else {
                console.error('❌ Error en respuesta del servidor:', data.error);
                this.showErrorState('Error cargando formularios enviados');
            }

        } catch (error) {
            console.error('❌ Error cargando formularios enviados:', error);
            this.showErrorState('Error de conexión al servidor');
        } finally {
            this.isLoading = false;
            this.hideLoadingState();
        }
    }

    renderSubmittedForms() {
        console.log('🎨 Renderizando formularios enviados...');
        
        const container = document.getElementById('submitted-forms-grid');
        const noFormsMessage = document.getElementById('no-submitted-forms');

        if (!container) {
            console.error('❌ No se encontró el contenedor de formularios enviados');
            return;
        }

        // Limpiar contenido anterior
        container.innerHTML = '';

        if (this.submittedForms.length === 0) {
            // Mostrar mensaje de no hay formularios
            noFormsMessage.style.display = 'block';
            return;
        }

        // Ocultar mensaje de no hay formularios
        noFormsMessage.style.display = 'none';

        // Crear tarjetas para cada formulario
        this.submittedForms.forEach((form, index) => {
            const card = this.createSubmittedFormCard(form, index);
            container.appendChild(card);
        });

        console.log(`✅ Renderizados ${this.submittedForms.length} formularios`);
    }

    createSubmittedFormCard(form, index) {
        const card = document.createElement('div');
        card.className = 'form-card submitted-form';
        card.dataset.formId = form.id;
        card.style.animationDelay = `${index * 0.1}s`;

        // Formatear fecha
        const createdDate = new Date(form.created_at).toLocaleDateString('es-ES', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });

        card.innerHTML = `
            <div class="form-card-header submitted"></div>
            <div class="form-card-body">
                <div class="form-status ${form.status}">${form.status}</div>
                <h3 class="form-title">BSI PW1100 - ${form.work_order_number}</h3>
                <p class="form-subtitle">
                    ${form.aircraft_registration} | ${form.engine_sn}
                </p>
                
                <div class="form-metadata">
                    <div class="metadata-item">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Enviado: ${createdDate}</span>
                    </div>
                    <div class="metadata-item">
                        <i class="fas fa-user"></i>
                        <span>${form.inspected_by}</span>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button class="btn-edit" onclick="submittedFormsManager.editForm('${form.id}')">
                        <i class="fas fa-edit"></i>
                        Editar
                    </button>
                    <button class="btn-view-pdf" onclick="submittedFormsManager.viewPDF('${form.id}')">
                        <i class="fas fa-file-pdf"></i>
                        Ver PDF
                    </button>
                </div>
            </div>
        `;

        return card;
    }

    async editForm(formId) {
        console.log(`✏️ Editando formulario: ${formId}`);
        
        try {
            // Mostrar loading
            const card = document.querySelector(`[data-form-id="${formId}"]`);
            if (card) {
                const editBtn = card.querySelector('.btn-edit');
                const originalText = editBtn.innerHTML;
                editBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Cargando...';
                editBtn.disabled = true;

                // Restaurar botón después de un tiempo
                setTimeout(() => {
                    editBtn.innerHTML = originalText;
                    editBtn.disabled = false;
                }, 2000);
            }

            // Limpiar localStorage de edición previa si existe
            window.localStorageManager.clearFormData('edit', formId);
            window.localStorageManager.clearImages('edit', formId);

            // Navegar al formulario con parámetro de edición
            const editUrl = `forms/BSI_PW1100.html?edit=${formId}`;
            console.log(`🔗 Navegando a: ${editUrl}`);
            window.location.href = editUrl;

        } catch (error) {
            console.error('❌ Error al editar formulario:', error);
            this.showNotification('Error al abrir formulario para edición', 'error');
        }
    }

    async viewPDF(formId) {
        console.log(`📄 Viendo PDF del formulario: ${formId}`);
        
        try {
            // Buscar el formulario en la lista
            const form = this.submittedForms.find(f => f.id === formId);
            if (!form || !form.pdf_s3_key) {
                this.showNotification('PDF no disponible', 'error');
                return;
            }

            // Aquí podrías implementar la lógica para obtener la URL del PDF desde S3
            // Por ahora, mostraremos una notificación
            this.showNotification('Funcionalidad de ver PDF en desarrollo', 'info');

        } catch (error) {
            console.error('❌ Error al ver PDF:', error);
            this.showNotification('Error al acceder al PDF', 'error');
        }
    }

    filterSubmittedForms(query) {
        console.log(`🔍 Filtrando formularios: "${query}"`);
        
        if (!query.trim()) {
            // Mostrar todos los formularios
            this.renderSubmittedForms();
            return;
        }

        const filteredForms = this.submittedForms.filter(form => {
            const searchText = query.toLowerCase();
            return (
                form.work_order_number.toLowerCase().includes(searchText) ||
                form.aircraft_registration.toLowerCase().includes(searchText) ||
                form.engine_sn.toLowerCase().includes(searchText) ||
                form.inspected_by.toLowerCase().includes(searchText)
            );
        });

        // Renderizar formularios filtrados
        const container = document.getElementById('submitted-forms-grid');
        const noFormsMessage = document.getElementById('no-submitted-forms');

        if (!container) return;

        container.innerHTML = '';

        if (filteredForms.length === 0) {
            // Mostrar mensaje de no hay resultados
            const noResults = document.createElement('div');
            noResults.className = 'no-forms-message';
            noResults.innerHTML = `
                <div class="no-forms-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3>No se encontraron formularios</h3>
                <p>No hay formularios que coincidan con "${query}"</p>
            `;
            container.appendChild(noResults);
            return;
        }

        // Crear tarjetas para formularios filtrados
        filteredForms.forEach((form, index) => {
            const card = this.createSubmittedFormCard(form, index);
            container.appendChild(card);
        });
    }

    showLoadingState() {
        const loadingSpinner = document.getElementById('loading-submitted');
        if (loadingSpinner) {
            loadingSpinner.style.display = 'flex';
        }
    }

    hideLoadingState() {
        const loadingSpinner = document.getElementById('loading-submitted');
        if (loadingSpinner) {
            loadingSpinner.style.display = 'none';
        }
    }

    showErrorState(message) {
        const container = document.getElementById('submitted-forms-grid');
        if (!container) return;

        container.innerHTML = `
            <div class="no-forms-message">
                <div class="no-forms-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3>Error</h3>
                <p>${message}</p>
                <button onclick="submittedFormsManager.loadSubmittedForms()" 
                        style="margin-top: 1rem; padding: 0.5rem 1rem; background: var(--viva-green); color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Intentar nuevamente
                </button>
            </div>
        `;
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            max-width: 400px;
            background: ${type === 'error' ? '#e74c3c' : type === 'success' ? '#00a650' : '#007bff'};
            animation: slideInFromRight 0.3s ease-out;
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Remover notificación después de 4 segundos
        setTimeout(() => {
            notification.remove();
        }, 4000);
    }

    // Método para uso desde otros componentes
    async refreshSubmittedForms() {
        console.log('🔄 Refrescando formularios enviados...');
        this.submittedForms = [];
        await this.loadSubmittedForms();
    }
}

// Inicializar el gestor de formularios enviados
const submittedFormsManager = new SubmittedFormsManager();

// Hacer disponible globalmente
window.submittedFormsManager = submittedFormsManager;

// Agregar estilos de animación dinámicamente
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInFromRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);

console.log('✅ Sistema de formularios enviados inicializado'); 