(function(){
    /**
     * Renderiza la primera página de un Blob PDF dentro de un canvas.
     * @param {Blob} blob - Blob del PDF a mostrar.
     * @param {String} canvasId - ID del canvas donde dibujar.
     * @param {Number} pageNum - Número de página a renderizar (1-based).
     */
    async function renderPdfBlob(blob, canvasId = 'pdfCanvas', pageNum = 1){
        if(!window.pdfjsLib){
            console.error('PDF.js no está cargado');
            return;
        }
        const url = URL.createObjectURL(blob);
        try{
            const pdf = await pdfjsLib.getDocument(url).promise;
            pageNum = Math.min(Math.max(pageNum,1), pdf.numPages);
            const page = await pdf.getPage(pageNum);
            const viewport = page.getViewport({scale: 1.2});
            const canvas = document.getElementById(canvasId);
            if(!canvas){
                console.error('Canvas de destino no encontrado');
                return;
            }
            const ctx = canvas.getContext('2d');
            canvas.width  = viewport.width;
            canvas.height = viewport.height;
            await page.render({canvasContext: ctx, viewport}).promise;
        }catch(err){
            console.error('Error renderizando PDF:', err);
        }finally{
            URL.revokeObjectURL(url);
        }
    }

    // Exportar en window para uso global
    window.pdfPreview = { renderPdfBlob };
})(); 