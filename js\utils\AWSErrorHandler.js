/**
 * ====================================================
 * AWS ERROR HANDLER
 * Manejo centralizado de errores de AWS con fallback local
 * ====================================================
 */

class AWSErrorHandler {
    constructor() {
        this.fallbackStorage = new Map();
        this.errorCount = 0;
        this.lastError = null;
        this.maxRetries = 3;
        this.retryDelay = 1000;
        
        this.init();
    }

    init() {
        console.log('🛡️ Iniciando AWSErrorHandler...');
        console.log('📦 Fallback local habilitado para errores de AWS');
    }

    /**
     * Manejar error de AWS con fallback automático
     * @param {Error} error - Error de AWS
     * @param {string} operation - Operación que falló
     * @param {Object} data - Datos de la operación
     * @returns {Object} - Resultado del fallback
     */
    async handleAWSError(error, operation, data = {}) {
        this.errorCount++;
        this.lastError = {
            error: error.message,
            operation,
            timestamp: new Date().toISOString(),
            data
        };

        console.warn(`⚠️ Error AWS en ${operation}:`, error.message);
        console.log(`🔄 Ejecutando fallback local para ${operation}...`);

        switch (operation) {
            case 'upload':
                return await this.handleUploadFallback(data);
            
            case 'download':
                return await this.handleDownloadFallback(data);
            
            case 'delete':
                return await this.handleDeleteFallback(data);
            
            case 'getSignedUrl':
                return await this.handleSignedUrlFallback(data);
            
            default:
                console.error(`❌ No hay fallback para operación: ${operation}`);
                throw error;
        }
    }

    /**
     * Fallback para subida de archivos
     * @param {Object} data - Datos del archivo
     * @returns {Object} - Resultado simulado
     */
    async handleUploadFallback(data) {
        const { file, key, bucket } = data;
        
        try {
            // Generar ID único para el archivo
            const fileId = this.generateFileId(key);
            
            // Convertir archivo a base64 para almacenamiento local
            const base64Data = await this.fileToBase64(file);
            
            // Guardar en localStorage
            const fileData = {
                id: fileId,
                key: key,
                bucket: bucket,
                name: file.name,
                size: file.size,
                type: file.type,
                data: base64Data,
                uploadedAt: new Date().toISOString(),
                fallback: true
            };
            
            this.saveToLocalStorage(fileId, fileData);
            
            // Simular respuesta de AWS
            const mockResult = {
                Key: key,
                Location: `local://${fileId}`,
                Bucket: bucket,
                ETag: '"' + this.generateETag() + '"',
                fallback: true,
                localId: fileId
            };
            
            console.log(`✅ Archivo guardado localmente: ${file.name}`);
            return mockResult;
            
        } catch (error) {
            console.error('❌ Error en fallback de subida:', error);
            throw new Error(`Fallback de subida falló: ${error.message}`);
        }
    }

    /**
     * Fallback para descarga de archivos
     * @param {Object} data - Datos de descarga
     * @returns {Blob} - Blob del archivo
     */
    async handleDownloadFallback(data) {
        const { key, fileId } = data;
        
        try {
            // Buscar archivo en localStorage
            const fileData = this.getFromLocalStorage(fileId || this.keyToFileId(key));
            
            if (!fileData) {
                throw new Error(`Archivo no encontrado en almacenamiento local: ${key}`);
            }
            
            // Convertir base64 de vuelta a blob
            const blob = await this.base64ToBlob(fileData.data, fileData.type);
            
            console.log(`✅ Archivo recuperado localmente: ${fileData.name}`);
            return blob;
            
        } catch (error) {
            console.error('❌ Error en fallback de descarga:', error);
            throw new Error(`Fallback de descarga falló: ${error.message}`);
        }
    }

    /**
     * Fallback para eliminación de archivos
     * @param {Object} data - Datos de eliminación
     * @returns {Object} - Resultado de eliminación
     */
    async handleDeleteFallback(data) {
        const { key, fileId } = data;
        
        try {
            const targetId = fileId || this.keyToFileId(key);
            const removed = this.removeFromLocalStorage(targetId);
            
            if (removed) {
                console.log(`✅ Archivo eliminado localmente: ${key}`);
                return { success: true, fallback: true };
            } else {
                console.warn(`⚠️ Archivo no encontrado para eliminar: ${key}`);
                return { success: false, fallback: true, reason: 'not_found' };
            }
            
        } catch (error) {
            console.error('❌ Error en fallback de eliminación:', error);
            throw new Error(`Fallback de eliminación falló: ${error.message}`);
        }
    }

    /**
     * Fallback para URLs firmadas
     * @param {Object} data - Datos de URL firmada
     * @returns {string} - URL local
     */
    async handleSignedUrlFallback(data) {
        const { key, fileId } = data;
        
        try {
            const targetId = fileId || this.keyToFileId(key);
            const fileData = this.getFromLocalStorage(targetId);
            
            if (!fileData) {
                throw new Error(`Archivo no encontrado para URL firmada: ${key}`);
            }
            
            // Crear URL de datos para acceso directo
            const dataUrl = `data:${fileData.type};base64,${fileData.data}`;
            
            console.log(`✅ URL local generada para: ${fileData.name}`);
            return dataUrl;
            
        } catch (error) {
            console.error('❌ Error en fallback de URL firmada:', error);
            throw new Error(`Fallback de URL firmada falló: ${error.message}`);
        }
    }

    // ====================================================
    // MÉTODOS UTILITARIOS
    // ====================================================

    /**
     * Convertir archivo a base64
     * @param {File} file - Archivo a convertir
     * @returns {Promise<string>} - Datos en base64
     */
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                // Remover prefijo data:type;base64,
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    /**
     * Convertir base64 a blob
     * @param {string} base64 - Datos en base64
     * @param {string} type - Tipo MIME
     * @returns {Promise<Blob>} - Blob del archivo
     */
    async base64ToBlob(base64, type) {
        const response = await fetch(`data:${type};base64,${base64}`);
        return await response.blob();
    }

    /**
     * Generar ID único para archivo
     * @param {string} key - Clave del archivo
     * @returns {string} - ID único
     */
    generateFileId(key) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2);
        const cleanKey = key.replace(/[^a-zA-Z0-9]/g, '_');
        return `${timestamp}_${random}_${cleanKey}`;
    }

    /**
     * Convertir key a fileId
     * @param {string} key - Clave del archivo
     * @returns {string} - ID del archivo
     */
    keyToFileId(key) {
        // Buscar en localStorage por key
        for (let i = 0; i < localStorage.length; i++) {
            const storageKey = localStorage.key(i);
            if (storageKey.startsWith('viva_file_')) {
                const data = JSON.parse(localStorage.getItem(storageKey));
                if (data.key === key) {
                    return data.id;
                }
            }
        }
        return null;
    }

    /**
     * Generar ETag simulado
     * @returns {string} - ETag
     */
    generateETag() {
        return Math.random().toString(36).substring(2, 15);
    }

    /**
     * Guardar en localStorage
     * @param {string} fileId - ID del archivo
     * @param {Object} data - Datos a guardar
     */
    saveToLocalStorage(fileId, data) {
        const key = `viva_file_${fileId}`;
        localStorage.setItem(key, JSON.stringify(data));
        this.fallbackStorage.set(fileId, data);
    }

    /**
     * Obtener de localStorage
     * @param {string} fileId - ID del archivo
     * @returns {Object|null} - Datos del archivo
     */
    getFromLocalStorage(fileId) {
        if (!fileId) return null;
        
        // Primero buscar en cache
        if (this.fallbackStorage.has(fileId)) {
            return this.fallbackStorage.get(fileId);
        }
        
        // Buscar en localStorage
        const key = `viva_file_${fileId}`;
        const data = localStorage.getItem(key);
        
        if (data) {
            const parsed = JSON.parse(data);
            this.fallbackStorage.set(fileId, parsed);
            return parsed;
        }
        
        return null;
    }

    /**
     * Eliminar de localStorage
     * @param {string} fileId - ID del archivo
     * @returns {boolean} - True si se eliminó
     */
    removeFromLocalStorage(fileId) {
        if (!fileId) return false;
        
        const key = `viva_file_${fileId}`;
        const existed = localStorage.getItem(key) !== null;
        
        localStorage.removeItem(key);
        this.fallbackStorage.delete(fileId);
        
        return existed;
    }

    /**
     * Obtener estadísticas de errores
     * @returns {Object} - Estadísticas
     */
    getErrorStats() {
        return {
            errorCount: this.errorCount,
            lastError: this.lastError,
            fallbackFiles: this.fallbackStorage.size,
            localStorageUsed: this.getLocalStorageUsage()
        };
    }

    /**
     * Obtener uso de localStorage
     * @returns {Object} - Información de uso
     */
    getLocalStorageUsage() {
        let totalSize = 0;
        let fileCount = 0;
        
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key.startsWith('viva_file_')) {
                const data = localStorage.getItem(key);
                totalSize += data.length;
                fileCount++;
            }
        }
        
        return {
            fileCount,
            totalSize,
            totalSizeMB: (totalSize / (1024 * 1024)).toFixed(2)
        };
    }

    /**
     * Limpiar archivos de fallback antiguos
     * @param {number} maxAge - Edad máxima en días
     */
    cleanupOldFiles(maxAge = 7) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - maxAge);
        
        let cleaned = 0;
        
        for (let i = localStorage.length - 1; i >= 0; i--) {
            const key = localStorage.key(i);
            if (key.startsWith('viva_file_')) {
                try {
                    const data = JSON.parse(localStorage.getItem(key));
                    const uploadDate = new Date(data.uploadedAt);
                    
                    if (uploadDate < cutoffDate) {
                        localStorage.removeItem(key);
                        this.fallbackStorage.delete(data.id);
                        cleaned++;
                    }
                } catch (error) {
                    // Eliminar entradas corruptas
                    localStorage.removeItem(key);
                    cleaned++;
                }
            }
        }
        
        console.log(`🧹 ${cleaned} archivos de fallback antiguos eliminados`);
        return cleaned;
    }
}

// Crear instancia global
const awsErrorHandler = new AWSErrorHandler();

// Hacer disponible globalmente
window.awsErrorHandler = awsErrorHandler;

console.log('✅ AWSErrorHandler inicializado');
