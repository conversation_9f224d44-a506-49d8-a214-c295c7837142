/*
 * CSS Común para todos los formularios en la carpeta forms/
 * Archivo creado para evitar duplicación de código CSS
 */
 
.header {
    background-color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.logo {
    height: 40px;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-weight: 500;
    color: var(--dark-gray);
}

.user-icon {
    width: 30px;
    height: 30px;
    background-color: var(--dark-gray);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}


:root {
    --primary-color: #0078d4; /* Microsoft Blue */
    --border-color: #c8c6c4;
    --input-bg-color: #fff;
    --input-focus-border: #005a9e;
    --text-color: #323130;
    --label-color: #323130;
    --placeholder-color: #605e5c;
    --error-color: #a80000;
    --body-bg-color: #f3f2f1; /* Light gray background */
    --form-bg-color: #fff;
    --section-border-color: #eaeaea;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: var(--body-bg-color);
    color: var(--text-color);
    line-height: 1.6;
}

.form-container {
    max-width: 800px;
    margin: 20px auto;
    background-color: var(--form-bg-color);
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

h1 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 25px;
    font-size: 2em;
    font-weight: 600;
}

.form-subtitle {
    text-align: center;
    color: var(--placeholder-color);
    font-size: 0.9em;
    margin-bottom: 25px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.form-note {
    font-size: 0.9em;
    color: var(--placeholder-color);
    margin-bottom: 15px;
    padding-left: 5px;
}

.form-note.header-note {
    text-align: center;
    margin-top: -15px;
}

fieldset {
    border: none; /* Remove default fieldset border */
    padding: 0;
    margin-bottom: 30px;
}

legend {
    font-weight: 600;
    font-size: 1.4em;
    color: var(--primary-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
    width: 100%;
    border-bottom: 1px solid var(--section-border-color);
}

.form-group {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid var(--section-border-color);
    border-radius: 4px;
    background-color: #fdfdfd; /* Slightly off-white for question groups */
}

label, .radio-group-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 1em;
    color: var(--label-color);
}

input[type="text"],
input[type="date"],
input[type="email"],
input[type="number"],
textarea,
select {
    width: 100%;
    padding: 10px 12px;
    margin-bottom: 5px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 1em;
    background-color: var(--input-bg-color);
    color: var(--text-color);
}

input[type="text"]::placeholder,
textarea::placeholder {
    color: var(--placeholder-color);
}

input[type="text"]:focus,
input[type="date"]:focus,
input[type="email"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.3);
}

textarea {
    min-height: 80px;
    resize: vertical;
}

.radio-group div, .checkbox-group div {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.radio-group input[type="radio"], .checkbox-group input[type="checkbox"] {
    margin-right: 10px;
    width: auto; /* Override full width for radios/checkboxes */
    accent-color: var(--primary-color); /* Modern way to color radio/checkbox */
}

.radio-group label, .checkbox-group label {
    font-weight: normal;
    margin-bottom: 0; /* Reset margin for inline labels */
}

.asterisk {
    color: var(--error-color);
    margin-left: 2px;
}

button[type="submit"] {
    display: block;
    width: auto;
    min-width: 150px;
    padding: 12px 25px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1.1em;
    font-weight: 600;
    transition: background-color 0.2s ease-in-out;
    margin: 30px auto 0; /* Center button */
}

button[type="submit"]:hover {
    background-color: var(--input-focus-border);
}

.footer-text {
    font-size:0.8em;
    color: var(--placeholder-color);
    text-align: center;
    margin-top:40px;
}

.microsoft-forms-logo { /* Placeholder for potential logo */
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    font-size: 0.9em;
    color: var(--placeholder-color);
}

.microsoft-forms-logo img {
    height: 16px;
    margin-right: 8px;
}

/* Estilos específicos para el formulario spotcheck */
.spotcheck h1 {
    margin-bottom: 10px; /* Reduced margin for subtitle in spotcheck */
}

/* Estilos para el componente de búsqueda en selects */
.searchable-select-container {
    position: relative;
    width: 100%;
}

.searchable-select-container .search-input {
    width: 100%;
    padding: 10px 12px;
    margin-bottom: 5px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 1em;
    background-color: var(--input-bg-color);
    color: var(--text-color);
}

/* Cuando las opciones están visibles, ajustar el borde del input */
.searchable-select-container .search-input.options-visible {
    border-radius: 4px 4px 0 0;
    border-bottom: none;
}

.searchable-select-container .search-input:focus {
    outline: none;
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.3);
}

.searchable-select-container .search-input::placeholder {
    color: var(--placeholder-color);
}

.searchable-select-container .options-container {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 4px 4px;
    background-color: var(--input-bg-color);
    position: absolute;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.searchable-select-container .option-item {
    padding: 10px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.searchable-select-container .option-item:hover {
    background-color: #f5f5f5;
}

.searchable-select-container .option-item.selected {
    background-color: var(--primary-color);
    color: white;
}

.searchable-select-container .option-item.highlighted {
    background-color: #e3f2fd;
    border-left: 3px solid var(--primary-color);
}

.searchable-select-container .option-item.hidden {
    display: none;
}

.searchable-select-container .no-results {
    padding: 10px 12px;
    color: var(--placeholder-color);
    font-style: italic;
    text-align: center;
}

/* Ocultar el select original cuando se convierte a searchable */
select.original-select {
    display: none !important;
}

/* Estilos para la sección de imágenes */
.image-upload-input {
    margin-bottom: 10px !important;
    padding: 12px !important;
    border: 2px dashed var(--border-color) !important;
    border-radius: 6px !important;
    background-color: #fafafa !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.image-upload-input:hover {
    border-color: var(--primary-color) !important;
    background-color: #f0f8ff !important;
}

.image-upload-input:focus {
    border-color: var(--input-focus-border) !important;
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.3) !important;
}

.image-preview-container {
    margin-top: 15px;
}

.image-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0 10px 0;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border: 1px solid var(--section-border-color);
    border-radius: 4px;
}

.image-preview-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.1em;
    font-weight: 600;
}

.image-preview-item {
    display: flex;
    align-items: flex-start;
    margin: 15px 0;
    padding: 20px;
    border: 1px solid var(--section-border-color);
    border-radius: 6px;
    background-color: #fdfdfd;
    position: relative;
    transition: box-shadow 0.2s ease;
}

.image-preview-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.image-preview-thumbnail {
    flex-shrink: 0;
    margin-right: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.image-preview-thumbnail img {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.image-filename {
    margin: 8px 0 0 0;
    font-size: 0.8em;
    color: var(--placeholder-color);
    word-break: break-all;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Estilos para overlay de imágenes */
.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    color: white;
    padding: 8px;
    border-radius: 0 0 6px 6px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-preview-thumbnail:hover .image-overlay {
    opacity: 1;
}

.image-overlay .image-filename {
    color: white;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    margin: 0;
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: none;
}

.image-actions {
    display: flex;
    gap: 4px;
    margin-left: 8px;
}

.image-overlay .btn-remove-image {
    background-color: rgba(220, 53, 69, 0.9);
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: background-color 0.2s;
    margin: 0;
    width: auto;
}

.image-overlay .btn-remove-image:hover {
    background-color: rgba(200, 35, 51, 1);
}

.image-description-container {
    flex-grow: 1;
}

.image-description-container .image-description-label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--label-color);
    font-size: 1em;
}

.image-description-container textarea {
    width: 100%;
    height: 80px;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    resize: vertical;
    font-family: inherit;
    font-size: 14px;
    background-color: var(--input-bg-color);
    color: var(--text-color);
    box-sizing: border-box;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.image-description-container textarea:focus {
    outline: none;
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 2px rgba(0, 120, 212, 0.3);
}

.image-description-container textarea::placeholder {
    color: var(--placeholder-color);
    font-style: italic;
}

.image-description-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    gap: 10px;
}

.char-count {
    font-size: 0.75em;
    color: var(--placeholder-color);
    font-weight: 500;
    white-space: nowrap;
}

.image-description-help {
    margin: 0;
    font-size: 0.8em;
    color: var(--placeholder-color);
    font-style: italic;
    flex-grow: 1;
}

/* Estilos para cuando no hay imágenes */
.no-images-message {
    text-align: center;
    padding: 40px 20px;
    color: var(--placeholder-color);
    font-size: 1.1em;
}

.no-images-message p {
    margin: 8px 0;
}

.no-images-message .text-muted {
    color: var(--placeholder-color);
    font-size: 0.9em;
    font-style: italic;
}

/* Botones de acción */
.action-buttons-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin: 30px 0;
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid var(--section-border-color);
    border-radius: 6px;
    flex-wrap: wrap;
}

.action-button {
    padding: 12px 25px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1.1em;
    font-weight: 600;
    transition: all 0.2s ease-in-out;
    min-width: 180px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.action-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-button.primary {
    background-color: var(--primary-color);
    color: white;
}

.action-button.primary:hover {
    background-color: var(--input-focus-border);
}

.action-button.secondary {
    background-color: #6c757d;
    color: white;
}

.action-button.secondary:hover {
    background-color: #5a6268;
}

.action-button.warning {
    background-color: #ff6b35;
    color: white;
}

.action-button.warning:hover {
    background-color: #e55a2b;
}

.action-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

/* Botones pequeños para eliminar imágenes */
.btn-remove-image {
    width: 100%;
    margin-top: 8px;
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.btn-remove-image:hover {
    background-color: #c82333;
}

.btn-remove-all {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: background-color 0.2s ease;
}

.btn-remove-all:hover {
    background-color: #c82333;
}

/* ===== MODAL DE PREVISUALIZACIÓN PROFESIONAL ===== */

/* Modal principal - Pantalla completa optimizada */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85); /* Fondo más oscuro para mejor contraste */
    backdrop-filter: blur(3px); /* Efecto de desenfoque moderno */
    justify-content: center;
    align-items: center;
    animation: modalFadeIn 0.3s ease-out;
}

/* Bloquear scroll del body cuando modal está abierto */
body.modal-open {
    overflow: hidden !important;
    height: 100vh !important;
}

/* CRÍTICO: Asegurar que el body mantenga sus estilos correctos cuando NO hay modal */
body:not(.modal-open) {
    overflow: auto !important;
    height: auto !important;
    margin: 0 !important;
    padding: 20px !important;
    background-color: var(--body-bg-color) !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif !important;
    color: var(--text-color) !important;
    line-height: 1.6 !important;
    display: block !important;
    position: static !important;
    justify-content: unset !important;
    align-items: unset !important;
    min-height: unset !important;
}

/* Contenedor principal del modal - 10% más pequeño, sin header */
.modal-content {
    background-color: #ffffff;
    width: 85vw; /* 10% más pequeño */
    max-width: 1100px; /* Límite máximo ajustado */
    height: 85vh; /* 10% más pequeño */
    margin: 7.5vh auto; /* Centrado vertical */
    border: none;
    border-radius: 12px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: modalSlideIn 0.4s ease-out;
    position: relative;
}

/* Header del modal - OCULTO para maximizar espacio del documento */
.modal-header {
    display: none !important; /* Completamente oculto */
}

.modal-header h2 {
    margin: 0;
    font-size: 1.4em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-header h2:before {
    content: "📄";
    font-size: 1.2em;
}

/* Botón de cerrar mejorado */
.close-button {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    line-height: 1;
}

.close-button:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: rotate(90deg);
}

/* Cuerpo del modal - Área de visualización optimizada */
.modal-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: #f8f9fa;
    position: relative; /* Para posicionar el botón X */
}

/* Botón X flotante real para cerrar modal */
.floating-close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.floating-close-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

/* Contenedor de páginas - MAXIMIZADO para mostrar el documento */
#previewPagesContainer {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px; /* Menos padding para más espacio */
    overflow: hidden;
    background: #f8f9fa; /* Fondo más simple */
    position: relative;
    width: 100%;
    height: 100%;
}

/* Preview-page styles removed - obsolete HTML preview system eliminated */

/* Estilos específicos para la generación de PDF */
.pdf-page {
    width: 21cm !important;
    height: 29.7cm !important;
    background: #ffffff !important;
    display: block !important;
    visibility: visible !important;
    position: static !important;
    page-break-after: always;
    margin: 0 !important;
    padding: 0 !important;
    overflow: visible !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    transform: none !important;
    zoom: 1 !important;
}

.pdf-page * {
    visibility: visible !important;
    opacity: 1 !important;
    color: #333 !important;
}

.pdf-page .page-content-container,
.pdf-page .container,
.pdf-page .page-container {
    width: 100% !important;
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    overflow: visible !important;
    background: transparent !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    padding: 2cm !important;
    transform: none !important;
    zoom: 1 !important;
}

/* Obsolete preview-page styles removed */

/* Navegación de páginas - Compacta pero funcional */
.page-navigation {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-top: 1px solid #dee2e6;
    padding: 15px 25px; /* Más compacto para maximizar espacio de documento */
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    min-height: 70px; /* Más pequeño */
}

.page-navigation button {
    background: linear-gradient(135deg, var(--primary-color) 0%, #005a9e 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 120, 212, 0.3);
    min-width: 120px;
    justify-content: center;
}

.page-navigation button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 120, 212, 0.4);
}

.page-navigation button:active {
    transform: translateY(0);
}

.page-navigation button:disabled {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    cursor: not-allowed;
    transform: none !important;
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.2);
}

.page-navigation button:before {
    font-size: 16px;
}

.page-navigation button#prevPageBtn:before {
    content: "⬅️";
}

.page-navigation button#nextPageBtn:after {
    content: "➡️";
}

/* Indicador de página - Elegante y claro */
#pageNumberDisplay {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 12px 20px;
    border-radius: 20px;
    font-weight: 700;
    font-size: 16px;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 150px;
    justify-content: center;
}

#pageNumberDisplay:before {
    content: "📖";
    font-size: 18px;
}

/* Indicador de carga profesional */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    z-index: 3000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Animaciones profesionales */
@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalFadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes modalSlideIn {
    from { 
        opacity: 0;
        transform: scale(0.9) translateY(-50px);
    }
    to { 
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scroll personalizado para el contenedor de páginas */
#previewPagesContainer::-webkit-scrollbar {
    width: 12px;
}

#previewPagesContainer::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 6px;
}

#previewPagesContainer::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color) 0%, #005a9e 100%);
    border-radius: 6px;
}

#previewPagesContainer::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #005a9e 0%, var(--primary-color) 100%);
}

/* Estilos para el grupo de botones 2x2 */
.button-group {
    display: grid;
    grid-template-columns: 1fr; /* Una columna para las filas */
    gap: 20px; /* Espacio entre las filas de botones */
    margin-top: 30px;
    padding: 15px; /* Añade un poco de espacio alrededor del grupo de botones */
    border: 1px solid var(--section-border-color);
    border-radius: 4px;
    background-color: #fdfdfd;
}

.button-row {
    display: flex;
    justify-content: space-around; /* Distribuye los botones equitativamente con espacio a su alrededor */
    gap: 15px; /* Espacio entre los botones dentro de una fila */
    flex-wrap: wrap; /* Permite que los botones se envuelvan en pantallas más pequeñas */
}

/* Asegura que los botones tomen el mismo espacio */
.button-row .action-button {
    flex: 1; /* Permite que los botones crezcan y se encojan */
    min-width: 180px; /* Ancho mínimo para evitar que se hagan demasiado pequeños */
    max-width: 250px; /* Ancho máximo para evitar que se hagan demasiado anchos */
    box-sizing: border-box; /* Incluye el padding y el borde en el ancho y alto total del elemento */
}

/* ===== ESTILOS ESPECÍFICOS PARA PDF - PRESERVAR DISEÑO DEL PREVIEW ===== */
.pdf-page .container {
    background-color: white !important;
    width: 21cm !important;
    height: 29.7cm !important;
    box-shadow: none !important;
    padding: 2cm !important;
    box-sizing: border-box !important;
    position: relative !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
    font-size: 11pt !important;
    color: #333 !important;
    margin: 0 !important;
    transform: none !important;
}

.pdf-page .background-logo {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 50% !important;
    max-width: 500px !important;
    opacity: 0.1 !important;
    z-index: 0 !important;
    pointer-events: none !important;
}

.pdf-page .header-top {
    width: 100% !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    margin-bottom: 2cm !important;
}

.pdf-page .header-left {
    text-align: left !important;
    font-size: 8pt !important;
    line-height: 1.2 !important;
}

.pdf-page .header-right-logo {
    width: 120px !important;
}

.pdf-page .main-title {
    font-size: 14pt !important;
    font-weight: bold !important;
    text-transform: uppercase !important;
    margin-top: 1cm !important;
    margin-bottom: 0.5cm !important;
    line-height: 1.4 !important;
}

.pdf-page .section-title {
    font-size: 18pt !important;
    font-weight: bold !important;
    margin-bottom: 1.5cm !important;
    text-transform: uppercase !important;
}

.pdf-page .field-group {
    line-height: 1.6 !important;
    margin-bottom: 1.5cm !important;
}

.pdf-page .field-label {
    font-weight: bold !important;
    font-size: 12pt !important;
    margin-bottom: 0.3cm !important;
}

.pdf-page .field-placeholder {
    font-size: 12pt !important;
    margin-top: 0.3cm !important;
    color: #666 !important;
    border-bottom: 1px dashed #ccc !important;
    display: inline-block !important;
    min-width: 150px !important;
    padding-bottom: 2px !important;
}

.pdf-page .footer-bottom {
    position: absolute !important;
    bottom: 0.3cm !important;
    width: calc(100% - 4cm) !important;
    text-align: center !important;
}

.pdf-page .footer-stamp {
    font-size: 12pt !important;
    font-weight: bold !important;
    margin-bottom: 1cm !important;
    line-height: 1.4 !important;
}

.pdf-page .footer-rev {
    font-size: 9pt !important;
    color: #555 !important;
}

/* ===== ESTILOS RESPONSIVE PARA IMÁGENES ===== */
@media (max-width: 768px) {
    .image-preview-item {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 15px;
    }
    
    .image-preview-thumbnail {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .image-preview-thumbnail img {
        width: 200px;
        height: 200px;
    }
    
    .image-description-container {
        width: 100%;
    }
    
    .image-preview-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .image-description-footer {
        flex-direction: column;
        gap: 5px;
        align-items: flex-start;
    }
    
    .char-count {
        align-self: flex-end;
    }
}

@media (max-width: 480px) {
    .image-preview-thumbnail img {
        width: 150px;
        height: 150px;
    }
    
    .image-preview-item {
        margin: 10px 0;
        padding: 10px;
    }
    
    .image-description-container textarea {
        height: 100px;
    }
    
    .image-preview-header h4 {
        font-size: 14px;
    }
}

/* ===== ANIMACIONES PARA MEJOR EXPERIENCIA ===== */
.image-preview-item {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== ESTILOS DE VALIDACIÓN ===== */
.image-description-container textarea.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

.image-description-container textarea.success {
    border-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);
}

.image-description-container textarea.warning {
    border-color: #ffc107;
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.25);
}

/* ===== INDICADOR DE CARGA PARA IMÁGENES ===== */
.image-loading {
    position: relative;
    overflow: hidden;
}

.image-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* ===== ESTILOS PARA NOTIFICACIONES ===== */
.image-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 9999;
    max-width: 400px;
    font-size: 14px;
    line-height: 1.4;
    animation: slideInRight 0.3s ease-out;
}

.image-notification.success {
    background: #28a745;
    color: white;
}

.image-notification.error {
    background: #dc3545;
    color: white;
}

.image-notification.warning {
    background: #ffc107;
    color: #212529;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===== MEJORAS PARA ACCESIBILIDAD ===== */
.image-preview-item:focus-within {
    outline: 2px solid var(--input-focus-border);
    outline-offset: 2px;
}

.btn-remove-image:focus,
.btn-remove-all:focus {
    outline: 2px solid var(--input-focus-border);
    outline-offset: 2px;
}


