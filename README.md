# 🛩️ Sistema de Formularios Aeronáuticos Vivaaerobus

**Sistema avanzado de formularios digitales para inspecciones aeronáuticas con generación automática de reportes PDF profesionales.**

## 🚀 Características Principales

- **📋 Formularios Aeronáuticos Especializados**: 7 formularios certificados para inspecciones NDT y BSI
- **📄 Generación PDF Avanzada**: Documentos multipágina con jsPDF + AutoTable
- **🖼️ Manejo Completo de Imágenes**: Subida, procesamiento y embedding en PDFs
- **👁️ Previsualización de Documentos**: Vista previa antes de generar PDF final
- **🗄️ Integración con Base de Datos**: Conexión a Azure SQL Database
- **🎨 Sistema de Logos**: Watermarks automáticos y branding corporativo
- **📱 Interfaz Responsiva**: Diseño moderno adaptable a dispositivos móviles

## 📋 Formularios Disponibles

1. **BSI V2500 Engines** (F-QC-019 Rev 2) - Inspección boroscópica V2500
2. **BSI PW1100 Engines** (F-QC-018 Rev 2) - Inspección boroscópica PW1100 ✅ **Con PDF completo**
3. **Eddy Current Test Form** (NDT-003, Rev 2) - Pruebas de corrientes parásitas
4. **Ultrasonic Test Form** (NDT-009, Rev 1) - Pruebas ultrasónicas
5. **Spot Check** - Verificaciones puntuales
6. **On The Job Training Record** - Registro de entrenamiento
7. **Delegation Authority Request** (F-QC-022) - Solicitud de autoridad

## 🔧 Requisitos Técnicos

- **Node.js** (versión 16 o superior)
- **npm** (incluido con Node.js)
- **Azure SQL Database** (opcional, para persistencia)
- **Navegador moderno** (Chrome, Firefox, Edge)

## 🛠️ Instalación y Configuración

1. **Clonar el repositorio**
   ```bash
   git clone [repository-url]
   cd VIVA_INSPECTORES
   ```

2. **Instalar dependencias**
   ```bash
   npm install
   ```

3. **Configurar base de datos (opcional)**
   ```bash
   # Probar conexión a Azure DB
   node testAzureDbConnection.js
   ```

4. **Iniciar el servidor**
   ```bash
   npm start          # Producción
   npm run dev        # Desarrollo con auto-recarga
   ```

5. **Acceder al sistema**
   ```
   http://localhost:3000
   ```

## 📁 Arquitectura del Sistema

```
VIVA_INSPECTORES/
├── 📊 diagramas/                    # Documentación de arquitectura
│   ├── diagrama_componentes.md      # Arquitectura de componentes
│   ├── diagrama_entidad_relacion.md # Modelo de datos
│   ├── diagrama_flujo_datos.md      # Flujo de información
│   └── viva_inspectores_dbml.dbml   # Esquema de base de datos
├── 🎨 css/                          # Estilos y diseño
│   ├── styles.css                   # Estilos principales
│   └── forms-common.css             # Estilos de formularios
├── 📋 forms/                        # Formularios aeronáuticos
│   ├── BSI_PW1100.html             # BSI PW1100 ✅ PDF completo
│   ├── BSI_V2500.html              # BSI V2500
│   ├── Eddy Current Test Form.html  # Corrientes parásitas
│   ├── ultra.html                   # Pruebas ultrasónicas
│   ├── spotcheck.html              # Spot checks
│   └── *.html                       # Formularios HTML principales
├── ⚙️ js/                           # Lógica de aplicación
│   ├── form-handlers/              # Manejadores especializados
│   │   ├── BSI_PW1100_Handler.js   # Handler principal BSI PW1100
│   │   └── BSI_PW1100/             # Módulos específicos
│   │       └── PdfManager.js       # Generador PDF avanzado
│   ├── generators/                 # Generadores de documentos
│   │   └── BSI_PW1100_Generator.js # Generador Word (legacy)
│   ├── utils/                      # Utilidades y herramientas
│   │   ├── PdfDebugger.js          # Herramientas de debug
│   │   ├── EmergencyPdfDiagnostic.js # Diagnóstico de errores
│   │   └── docx-analyzer.js        # Analizador de plantillas
│   ├── pdf-preview.js              # Sistema de previsualización
│   ├── scripts.js                  # Scripts principales
│   └── select-search.js            # Búsqueda en selects
├── 🖼️ img/                          # Recursos gráficos
│   └── Viva_Logo.svg.png           # Logo corporativo
├── 📄 plantilla_word/               # Plantillas Word (legacy)
│   ├── BSI_PW1100.docx            # Plantilla principal
│   └── BSI_PW1100_TEST.docx       # Plantilla de pruebas
├── 📁 uploads/                      # Archivos temporales
│   └── images/                     # Imágenes subidas
├── 🌐 index.html                    # Página principal
├── 🖥️ server.js                     # Servidor Express
├── 🔗 testAzureDbConnection.js     # Pruebas de BD
└── 📦 package.json                 # Dependencias del proyecto
```

## 🎯 Guía de Uso

### 🚀 Flujo Principal - BSI PW1100

1. **Acceso al Sistema**
   ```
   http://localhost:3000 → BSI Report Form (PW 1100 ENGINES)
   ```

2. **Completar Inspección**
   - ✅ **Información General**: Work Order, Aircraft Registration, Engine S/N
   - ✅ **Datos del Inspector**: Nombre, Stamp, Estación, Fecha
   - ✅ **Componentes**: Status y observaciones de cada etapa (LPC, HPC, HPT, LPT)
   - ✅ **Disposición Final**: Conclusiones y estado del motor

3. **Adjuntar Evidencias**
   - 📸 **Imágenes**: JPG, PNG, GIF (máx. 10MB c/u)
   - 📝 **Descripciones**: Texto explicativo por imagen
   - 🔢 **Límite**: Hasta 10 imágenes por reporte

4. **Generar Documento**
   - 👁️ **Previsualizar**: Ver documento antes de generar
   - 📄 **Descargar PDF**: Documento final multipágina
   - 💾 **Guardar**: Almacenamiento automático en BD (si está configurada)

### 📋 Otros Formularios

- **BSI V2500**: Similar a PW1100, específico para motores V2500
- **Eddy Current**: Formulario para pruebas de corrientes parásitas
- **Ultrasonic**: Registro de pruebas ultrasónicas
- **Spot Check**: Verificaciones rápidas de calidad

## 🔧 Tecnologías y Arquitectura

### 🛠️ Stack Tecnológico

**Backend:**
- **Node.js + Express**: Servidor web y API REST
- **Azure SQL Database**: Base de datos en la nube (mssql)
- **Multer**: Manejo de archivos y uploads
- **CORS + Body-Parser**: Middleware de seguridad y parsing

**Frontend:**
- **HTML5 + CSS3**: Interfaz responsiva moderna
- **JavaScript ES6+**: Lógica de cliente sin frameworks
- **Font Awesome**: Iconografía profesional

**Generación PDF:**
- **jsPDF**: Librería principal de generación PDF
- **jsPDF-AutoTable**: Tablas automáticas multipágina
- **html2pdf.js**: Conversión HTML a PDF (legacy)
- **Canvas API**: Procesamiento de imágenes y logos

### 🏗️ Arquitectura de Componentes

```mermaid
graph TD
    A[Cliente Web] --> B[Express Server]
    B --> C[Form Handlers]
    C --> D[PDF Manager]
    D --> E[jsPDF + AutoTable]
    B --> F[Azure SQL DB]
    B --> G[File Upload]
    G --> H[Image Processing]
    H --> D
```

### 📄 Estructura del PDF Generado

**Página 1 - Cover Sheet:**
- Logo corporativo y watermark
- Información básica de aeronave
- Datos del inspector y stamp

**Página 2 - Información General:**
- Tabla de datos de inspección
- Referencias utilizadas
- Inicio de tabla de componentes (LPC Stages 1-3, Bearing #3)

**Página 3 - Componentes Intermedios:**
- Continuación automática de tabla
- HPC Stages 4-8, Igniter, Fuel Nozzle
- CCH Inner/Outer, Ship Lap, HPT Vane/Stages

**Página 4 - Componentes Finales:**
- LPT Stages 1-3
- Final Disposition (caja de texto expandible)
- Engine Status After BSI

**Páginas 5+ - Imágenes de Soporte:**
- 2 imágenes por página (formato optimizado)
- Descripciones automáticas
- Headers repetidos en cada página

### 🎨 Características Avanzadas del PDF

- ✅ **Multipágina Automática**: AutoTable maneja overflow
- ✅ **Headers Repetidos**: Información clave en cada página
- ✅ **Logos Inteligentes**: Carga automática con fallback
- ✅ **Watermarks**: Logo semi-transparente de fondo
- ✅ **Tablas Responsivas**: Ajuste automático de contenido
- ✅ **Imágenes Embebidas**: Procesamiento y optimización automática
- ✅ **Footers Consistentes**: Numeración y datos corporativos

## 🔄 Desarrollo y Extensión

### 📋 Agregar Nuevos Formularios

1. **Crear el HTML del formulario**
   ```html
   <!-- forms/NuevoFormulario.html -->
   <form id="nuevo-formulario">
       <!-- Campos específicos del formulario -->
   </form>
   ```

2. **Desarrollar el manejador**
   ```javascript
   // js/form-handlers/NuevoFormulario_Handler.js
   class NuevoFormulario_Handler {
       constructor() {
           this.form = document.getElementById('nuevo-formulario');
           this.pdfManager = new PdfManager(this);
       }
       
       collectFormData() {
           // Lógica de recolección de datos
       }
   }
   ```

3. **Crear el generador PDF**
   ```javascript
   // Extender PdfManager o crear nuevo generador
   generateNuevoFormularioPdf(pdf, formData, logoData) {
       // Lógica específica de generación
   }
   ```

4. **Agregar ruta en servidor**
   ```javascript
   // server.js
   app.post('/generate-nuevo-formulario', upload.array('images', 10), async (req, res) => {
       // Implementar endpoint específico
   });
   ```

5. **Actualizar página principal**
   ```html
   <!-- index.html -->
   <div class="form-card">
       <a href="forms/NuevoFormulario.html">
           <h3>Nuevo Formulario</h3>
       </a>
   </div>
   ```

## 🐛 Solución de Problemas

### 🔍 Herramientas de Debugging

**Consola del Navegador:**
```javascript
// Verificar estado del sistema
PdfDebugger.logDebugInfo(window.handler);

// Probar generación PDF
await PdfDebugger.testPdfGeneration(window.handler);

// Resaltar elementos problemáticos
PdfDebugger.highlightProblematicElements(container);
```

### ❌ Errores Comunes

**PDF en Blanco:**
- ✅ **Solucionado**: Sistema de clonación mejorado en PdfManager.js
- 🔧 **Causa**: Elementos ocultos con `display: none`
- 💡 **Solución**: Contenedor temporal visible para html2pdf

**Error de Servidor 500:**
```bash
# Verificar dependencias
npm install

# Probar conexión BD
node testAzureDbConnection.js

# Revisar logs
npm start
```

**Imágenes no se cargan:**
- 📏 **Tamaño**: Máximo 10MB por imagen
- 🖼️ **Formatos**: JPG, PNG, GIF, WEBP
- 🔢 **Límite**: 10 imágenes por formulario
- 🌐 **CORS**: Verificar configuración de servidor

**Logo no aparece:**
```javascript
// Verificar carga de logo
const logoData = await pdfManager.loadLogoImage();
console.log('Logo loaded:', logoData.loaded);
```

## 📊 Monitoreo y Logs

### 🔍 Debugging en Tiempo Real

```javascript
// Estado del sistema
console.log('Handler:', window.handler);
console.log('PDF cargado:', window.handler.currentPdfDoc ? 'Sí' : 'No');

// Verificar librerías
console.log('jsPDF:', typeof window.jspdf);
console.log('AutoTable:', typeof window.jspdf.jsPDF.API.autoTable);

// Limpiar debugging
PdfDebugger.cleanupDebugHighlights();
```

### 📈 Métricas del Sistema

- **Formularios Activos**: 7 formularios especializados
- **Generación PDF**: BSI PW1100 completamente funcional
- **Base de Datos**: Azure SQL Database integrada
- **Uptime**: Servidor Express con auto-restart
- **Archivos Temporales**: Limpieza automática cada 5 segundos

## 🔒 Seguridad y Compliance

### 🛡️ Medidas de Seguridad

- ✅ **Validación de Archivos**: Solo imágenes permitidas
- ✅ **Límites de Tamaño**: 10MB máximo por imagen
- ✅ **Limpieza Automática**: Archivos temporales eliminados
- ✅ **CORS Configurado**: Acceso controlado a recursos
- ✅ **Sanitización**: Validación de inputs del formulario
- ✅ **Azure Security**: Base de datos en la nube segura

### 📋 Compliance Aeronáutico

- **F-QC-018 Rev 3**: BSI PW1100 Engines
- **F-QC-019 Rev 2**: BSI V2500 Engines  
- **NDT-003 Rev 2**: Eddy Current Testing
- **NDT-009 Rev 1**: Ultrasonic Testing
- **F-QC-022**: Delegation Authority

## 🚀 Roadmap y Mejoras Futuras

- [ ] **Generación PDF para V2500**: Extender funcionalidad a otros motores
- [ ] **Firma Digital**: Integración de firmas electrónicas
- [ ] **API REST**: Endpoints para integración externa
- [ ] **Dashboard Analytics**: Métricas de inspecciones
- [ ] **Mobile App**: Aplicación móvil nativa
- [ ] **OCR Integration**: Reconocimiento de texto en imágenes

---

**Desarrollado por**: Equipo de Calidad Vivaaerobus  
**Versión**: 2.0.0 (Sistema PDF Avanzado)  
**Última Actualización**: 2024  
**Soporte**: [Contacto interno Vivaaerobus] 