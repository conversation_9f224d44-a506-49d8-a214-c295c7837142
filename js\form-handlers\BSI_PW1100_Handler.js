class BSI_PW1100_Handler {
    constructor() {
        this.form = null;
        this.imageFiles = [];

        // Estado de PDF.js
        this.currentPdfDoc = null;
        this.currentPdfPage = 0;
        this.totalPdfPages = 0;
        this.currentPdfBlob = null;

        this.modal = null;
        this.closeButton = null;
        this.prevPageBtn = null;
        this.nextPageBtn = null;
        this.downloadPdfBtn = null;
        this.pageNumberDisplay = null;
        this.previewPagesContainer = null;
        
        // ====================================================
        // NUEVAS PROPIEDADES PARA EDICIÓN DE FORMULARIOS
        // ====================================================
        this.isEditMode = false;
        this.originalFormId = null;
        this.editingFormData = null;
        
        this.init();
        this.currentScale = 1.5;
        // PdfManager se inicializa después en setupForm()
    }

    init() {
        // Esperar a que el DOM esté listo ANTES de hacer cualquier cosa
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.ensureCorrectBodyStyles();
                this.setupForm();
                this.checkForEditMode();
                this.loadFormData();
                this.loadSavedImages();
            });
        } else {
            this.ensureCorrectBodyStyles();
            this.setupForm();
            this.checkForEditMode();
            this.loadFormData();
            this.loadSavedImages();
        }
    }

    ensureCorrectBodyStyles() {
        // Verificar que document.body existe antes de intentar acceder a él
        if (!document.body) {
            console.error('❌ document.body no está disponible aún');
            return;
        }

        try {
            // LIMPIEZA AGRESIVA: Remover TODAS las clases problemáticas
            document.body.classList.remove('modal-open');
            
            // LIMPIEZA AGRESIVA: Limpiar todos los estilos inline
            if (document.body.hasAttribute('style')) {
                document.body.removeAttribute('style');
            }
            document.body.style.cssText = '';
            
            // LIMPIEZA AGRESIVA: Restaurar propiedades específicas del body
            document.body.style.overflow = '';
            document.body.style.height = '';
            document.body.style.paddingRight = '';
            document.body.style.margin = '';
            document.body.style.padding = '';
            document.body.style.display = '';
            document.body.style.justifyContent = '';
            document.body.style.alignItems = '';
            document.body.style.minHeight = '';
            document.body.style.backgroundColor = '';
            document.body.style.position = '';
            document.body.style.fontFamily = '';
            document.body.style.color = '';
            document.body.style.lineHeight = '';
            
            // Remover cualquier elemento de estilo inline que pueda existir
            const inlineStyles = document.querySelectorAll('style[data-modal-styles]');
            inlineStyles.forEach(style => style.remove());
            
            // Asegurar que el body tenga los estilos correctos para el formulario
            console.log('✅ Estilos del body completamente restaurados');
        } catch (error) {
            console.error('❌ Error al restaurar estilos del body:', error);
        }
    }

    setupForm() {
        this.form = document.querySelector('form');
        if (!this.form) {
            console.error('No se encontró el formulario');
            return;
        }

        // Initialize modal elements
        this.modal = document.getElementById('previewModal');
        this.closeButton = document.querySelector('#previewModal .close-button');
        this.prevPageBtn = document.getElementById('prevPageBtn');
        this.nextPageBtn = document.getElementById('nextPageBtn');
        this.downloadPdfBtn = document.getElementById('downloadPdfBtn');
        this.pageNumberDisplay = document.getElementById('pageNumberDisplay');
        this.previewPagesContainer = document.getElementById('previewPagesContainer');

        // Agregar contenedor de botones de acción
        this.addActionButtonsContainer();
        
        // Agregar sección para subir imágenes
        this.addImageUploadSection();
        
        // Configurar eventos
        this.setupEventListeners();
        
        // Verificar librerías PDF (con un pequeño delay para asegurar que se carguen)
        setTimeout(() => {
            this.checkPDFLibraries();
            // Inicializar PdfManager después de que todo esté listo
            if (window.PdfManager) {
                this.pdfManager = new window.PdfManager(this);
                console.log('✅ PdfManager inicializado correctamente');
            } else {
                console.error('❌ PdfManager no está disponible');
            }
        }, 500);
    }

    checkPDFLibraries() {
        console.log('🔍 Verificando librerías PDF...');
        
        // Verificar html2canvas
        if (typeof window.html2canvas !== 'undefined') {
            console.log('✅ html2canvas cargado correctamente');
        } else {
            console.warn('⚠️ html2canvas no está cargado');
        }
        
        // Verificar jsPDF
        if (window.jspdf && window.jspdf.jsPDF) {
            console.log('✅ jsPDF cargado correctamente en window.jspdf.jsPDF');
        } else if (window.jsPDF) {
            console.log('✅ jsPDF cargado correctamente en window.jsPDF');
        } else {
            console.warn('⚠️ jsPDF no está cargado');
        }
    }

    addImageUploadSection() {
        // Verificar si la sección de imágenes ya existe (agregada desde HTML)
        const existingImageInput = document.getElementById('inspection_images');
        if (existingImageInput) {
            console.log('La sección de imágenes ya existe en el HTML, no es necesario crearla.');
            return; // La sección ya está, no necesitamos crearla
        }

        // Solo crear la sección si no existe
        console.log('Creando sección de imágenes dinámicamente...');

        const imageSection = document.createElement('fieldset');
        imageSection.innerHTML = `
            <legend>Imágenes Adjuntas</legend>
            <div class="form-group">
                <label for="inspection_images">📷 Subir imágenes de la inspección </label>
                <input type="file" id="inspection_images" name="inspection_images" 
                       multiple accept="image/*" class="image-upload-input">
                <p class="form-note">📁 Puede subir múltiples imágenes. Formatos soportados: JPG, PNG, GIF. Máximo 10MB por imagen.</p>
                <div id="image-preview" class="image-preview-container"></div>
            </div>
        `;

        // Insertar antes del contenedor de botones de acción existente
        const buttonGroup = this.form.querySelector('.button-group');
        if (buttonGroup) {
            this.form.insertBefore(imageSection, buttonGroup);
        } else {
            // Si no se encuentra el contenedor de botones, como fallback, intentar antes del botón submit
            const submitButton = this.form.querySelector('button[type="submit"]');
            if (submitButton) {
                this.form.insertBefore(imageSection, submitButton);
            } else {
                // Si no hay botón submit, simplemente añadir al final del formulario
                this.form.appendChild(imageSection);
            }
        }
    }

    addActionButtonsContainer() {
        const buttonGroup = this.form.querySelector('.button-group');
        if (!buttonGroup) {
            console.error('No se encontró el contenedor .button-group en el formulario.');
            return; // Salir si no se encuentra el contenedor
        }

        // Verificar si los botones ya existen (agregados desde HTML)
        const existingButtons = buttonGroup.querySelectorAll('.action-button');
        if (existingButtons.length > 0) {
            console.log('Los botones ya existen en el HTML, no es necesario crearlos.');
            return; // Los botones ya están, no necesitamos crearlos
        }

        // Solo crear botones si no existen
        console.log('Creando botones dinámicamente...');

        // Limpiar cualquier contenido existente en buttonGroup
        buttonGroup.innerHTML = '';

        // Crear botón para limpiar formulario
        const clearButton = document.createElement('button');
        clearButton.type = 'button';
        clearButton.className = 'action-button secondary clear-form-btn';
        clearButton.innerHTML = '🗑️ Limpiar Formulario';

        // Crear botón de debug
        const debugButton = document.createElement('button');
        debugButton.type = 'button';
        debugButton.className = 'action-button warning debug-fill-btn';
        debugButton.innerHTML = 'Llenar Formulario (Debug)';

        // Crear botón para generar Word
        const wordButton = document.createElement('button');
        wordButton.type = 'button';
        wordButton.className = 'action-button primary generate-word-btn';
        wordButton.innerHTML = '📄 Generar Documento Word';

        // Crear botón para previsualizar documento
        const previewButton = document.createElement('button');
        previewButton.type = 'button';
        previewButton.id = 'previewDocBtn'; // Mantener el ID si hay listeners asociados
        previewButton.className = 'action-button primary preview-doc-btn';
        previewButton.innerHTML = 'Previsualizar Documento';

        // Crear contenedores para las filas de botones
        const row1 = document.createElement('div');
        row1.className = 'button-row';
        row1.appendChild(clearButton);
        row1.appendChild(debugButton);

        const row2 = document.createElement('div');
        row2.className = 'button-row';
        row2.appendChild(wordButton);
        row2.appendChild(previewButton);

        // Añadir las filas al buttonGroup
        buttonGroup.appendChild(row1);
        buttonGroup.appendChild(row2);
    }

    setupEventListeners() {
        // Usar un pequeño timeout para asegurar que todos los elementos estén disponibles
        setTimeout(() => {
            this.configureEventListeners();
        }, 100);
    }

    configureEventListeners() {
        console.log('🔧 Configurando event listeners...');

        // Eventos para subir imágenes por sección
        this.configureSectionImageListeners();

        // Evento para previsualizar documento - abre modal con PDF renderizado
        const previewButton = document.getElementById('previewDocBtn');
        if (previewButton) {
            previewButton.addEventListener('click', () => this.openPreviewModal());
            console.log('✅ Event listener para previsualización configurado correctamente');
        } else {
            console.error('❌ No se encontró el botón de previsualización');
        }

        // Evento para generar documento Word - apunta al nuevo botón creado en JS
        const wordButton = document.querySelector('.generate-word-btn');
        if (wordButton) {
            wordButton.addEventListener('click', () => this.generateWordDocument());
            console.log('✅ Event listener para generar Word configurado correctamente');
        } else {
            console.error('❌ No se encontró el botón de generar Word');
        }

        // Evento para llenar formulario con datos de debug
        const debugButton = document.querySelector('.debug-fill-btn');
        if (debugButton) {
            debugButton.addEventListener('click', () => this.fillFormWithDebugData());
            console.log('✅ Event listener para debug configurado correctamente');
        } else {
            console.error('❌ No se encontró el botón de debug');
        }

        // Evento para limpiar formulario
        const clearButton = document.querySelector('.clear-form-btn');
        if (clearButton) {
            clearButton.addEventListener('click', () => this.clearForm());
            console.log('✅ Event listener para limpiar formulario configurado correctamente');
        } else {
            console.error('❌ No se encontró el botón de limpiar formulario');
        }

        // Prevenir envío normal del formulario si se quiere generar Word
        if (this.form) {
            this.form.addEventListener('submit', (e) => {
                // Permitir envío normal del formulario para otras funcionalidades
                console.log('Formulario enviado normalmente');
            });
            console.log('✅ Event listener para formulario configurado correctamente');
        } else {
            console.error('❌ No se encontró el formulario');
        }

        // Manejo del modal de previsualización PDF
        if (this.closeButton) {
            this.closeButton.addEventListener('click', () => {
                this.closePreviewModal();
            });
            console.log('✅ Event listener para cerrar modal configurado correctamente');
        } else {
            console.error('❌ No se encontró el botón de cerrar modal');
        }

        // Click fuera del modal para cerrarlo
        window.addEventListener('click', (event) => {
            if (event.target === this.modal) {
                this.closePreviewModal();
            }
        });

        // Escape key para cerrar modal
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.modal && this.modal.style.display !== 'none') {
                this.closePreviewModal();
            }
        });

        // Botón X flotante para cerrar modal
        const floatingCloseBtn = document.querySelector('.floating-close-btn');
        if (floatingCloseBtn) {
            floatingCloseBtn.addEventListener('click', () => {
                this.closePreviewModal();
            });
            console.log('✅ Event listener para botón X flotante configurado correctamente');
        } else {
            console.error('❌ No se encontró el botón X flotante');
        }

        // Eventos de navegación del modal
        // La navegación de PDF se asigna dinámicamente en openPreviewModal()

        if (this.downloadPdfBtn) {
            this.downloadPdfBtn.addEventListener('click', () => {
                if (this.pdfManager) {
                    this.pdfManager.download();
                } else {
                    console.error('❌ PdfManager no está inicializado');
                    alert('Error: Sistema PDF no está listo. Recarga la página.');
                }
            });
            console.log('✅ Event listener para descarga PDF configurado correctamente');
        } else {
            console.error('❌ No se encontró el botón de descarga PDF');
        }

        console.log('🎉 Configuración de event listeners completada');
    }

    /**
     * Configurar event listeners para inputs de imagen por sección
     */
    configureSectionImageListeners() {
        console.log('📸 Configurando event listeners para imágenes por sección...');
        
        // Lista de todas las secciones con inputs de imagen
        const sections = [
            'lpc_stage1', 'lpc_stage2', 'lpc_stage3',
            'bearing3_front', 'bearing3_rear',
            'hpc_stage1', 'hpc_stage2', 'hpc_stage3', 'hpc_stage4', 
            'hpc_stage5', 'hpc_stage6', 'hpc_stage7', 'hpc_stage8',
            'igniter', 'fuelnozzle', 'cch_inner', 'cch_outer',
            'shiplap',
            'hpt_vane', 'hpt_s1', 'hpt_s2',
            'lpt_s1', 'lpt_s2', 'lpt_s3'
        ];
        
        let configuredListeners = 0;
        
        sections.forEach(section => {
            const inputId = `${section}_images`;
            const imageInput = document.getElementById(inputId);
            
            if (imageInput) {
                imageInput.addEventListener('change', (e) => this.handleSectionImageUpload(e, section));
                configuredListeners++;
                console.log(`✅ Event listener configurado para sección: ${section}`);
            } else {
                console.warn(`⚠️ No se encontró input de imagen para sección: ${section} (ID: ${inputId})`);
            }
        });
        
        console.log(`📸 ${configuredListeners} event listeners de imagen configurados`);
        
        // Cargar previews existentes al inicializar
        this.loadExistingSectionPreviews();
    }

    /**
     * Manejar subida de imágenes para una sección específica
     * @param {Event} event - Evento del input file
     * @param {string} section - Sección del motor
     */
    async handleSectionImageUpload(event, section) {
        const files = Array.from(event.target.files);
        
        if (files.length === 0) return;
        
        console.log(`📸 Subiendo ${files.length} imagen(es) para sección ${section}...`);
        
        // Mostrar indicador de carga
        this.showLoadingIndicator(`Subiendo imágenes para ${section}...`);
        
        try {
            // Procesar archivos con ImageManager incluyendo la sección
            const mode = this.isEditMode ? 'edit' : 'draft';
            const formId = this.isEditMode ? this.originalFormId : null;
            
            const processedImages = await window.imageManager.processFiles(files, mode, formId, section);
            
            // Actualizar preview de la sección específica
            window.imageManager.updateSectionPreview(section, mode, formId);
            
            if (processedImages.length > 0) {
                window.imageManager.showSuccess(`${processedImages.length} imagen(es) subida(s) para ${section}`);
            }
            
        } catch (error) {
            console.error(`❌ Error procesando imágenes para ${section}:`, error);
            this.showErrorMessage(`Error procesando imágenes para ${section}: ` + error.message);
        } finally {
            this.hideLoadingIndicator();
        }
        
        // Limpiar el input para permitir seleccionar las mismas imágenes nuevamente si es necesario
        event.target.value = '';
    }

    /**
     * Cargar previews existentes de todas las secciones
     */
    loadExistingSectionPreviews() {
        console.log('🔄 Cargando previews existentes de secciones...');
        
        const mode = this.isEditMode ? 'edit' : 'draft';
        const formId = this.isEditMode ? this.originalFormId : null;
        
        // Actualizar todos los previews de sección
        window.imageManager.refreshAllSectionPreviews(mode, formId);
    }

    async handleImageUpload(event) {
        const files = Array.from(event.target.files);
        
        if (files.length === 0) return;
        
        console.log(`📸 Subiendo ${files.length} imagen(es)...`);
        
        // Mostrar indicador de carga
        this.showLoadingIndicator('Subiendo imágenes...');
        
        try {
            // Procesar archivos con ImageManager
            const mode = this.isEditMode ? 'edit' : 'draft';
            const formId = this.isEditMode ? this.originalFormId : null;
            
            const processedImages = await window.imageManager.processFiles(files, mode, formId);
            
            // Mostrar preview actualizado (ImageManager ya maneja el storage)
            this.refreshImagePreview();
            
            if (processedImages.length > 0) {
                window.imageManager.showSuccess(`${processedImages.length} imagen(es) subida(s) y guardada(s) en S3`);
            }
            
        } catch (error) {
            console.error('❌ Error procesando imágenes:', error);
            this.showErrorMessage('Error procesando imágenes: ' + error.message);
        } finally {
            this.hideLoadingIndicator();
        }
        
        // Limpiar el input para permitir seleccionar las mismas imágenes nuevamente si es necesario
        event.target.value = '';
    }



    /**
     * Actualizar previsualización de imágenes desde localStorage
     */
    refreshImagePreview() {
        const previewContainer = document.getElementById('image-preview');
        previewContainer.innerHTML = '';

        // Determinar modo y formId según URL
        const urlParams = new URLSearchParams(window.location.search);
        const mode = urlParams.get('edit') ? 'edit' : 'draft';
        const formId = urlParams.get('edit') || null;

        // Obtener imágenes con descripciones
        const images = window.imageManager.getImagesWithDescriptions(mode, formId);

        if (images.length === 0) {
            previewContainer.innerHTML = `
                <div class="no-images-message">
                    <p>📷 No hay imágenes seleccionadas</p>
                    <p class="text-muted">Seleccione imágenes para ver la previsualización aquí</p>
                </div>
            `;
            return;
        }

        // Agregar encabezado para la sección de imágenes
        const headerDiv = document.createElement('div');
        headerDiv.className = '';
        headerDiv.innerHTML = `
            <h4>📸 Imágenes seleccionadas (${images.length})</h4>
            <button type="button" onclick="window.bsiHandler.removeAllImages()" class="btn-remove-all">
                🗑️ Eliminar todas
            </button>
        `;
        previewContainer.appendChild(headerDiv);

        // Agregar cada imagen con su previsualización
        images.forEach((imageData, index) => {
            const imageHTML = window.imageManager.createImagePreviewHTML(imageData, index);
            const imageDiv = document.createElement('div');
            imageDiv.innerHTML = imageHTML;
            previewContainer.appendChild(imageDiv.firstElementChild);
        });
    }

    async openPreviewModal() {
        try {
            // Mostrar modal y spinner
            this.modal.style.display = 'flex';
            document.body.classList.add('modal-open');
            this.showModalLoadingIndicator();

            this.ensurePdfElements();

            const formData = this.collectFormData();
            this.saveFormData(formData);

            const validation = this.validateRequiredFields(formData);
            if (!validation.isValid) {
                this.hideModalLoadingIndicator();
                this.showValidationError(validation.missingFields);
                return;
            }

            // Verificar que PdfManager esté disponible
            if (!this.pdfManager) {
                throw new Error('PdfManager no está inicializado. Recarga la página.');
            }
            
            // Generar blob del PDF usando PdfManager
            const blob = await this.pdfManager.generateBlob(formData);
            this.currentPdfBlob = blob;

            // Crear documento PDF.js para conocer nº páginas
            const buffer = await blob.arrayBuffer();
            this.currentPdfDoc = await pdfjsLib.getDocument({ data: buffer }).promise;
            this.totalPdfPages = this.currentPdfDoc.numPages;
            this.currentPdfPage = 1;

            // Renderizar primera página
            await this.renderCurrentPdfPage();

            // Configurar navegación
            this.prevPageBtn.onclick = () => this.navigatePdf(-1);
            this.nextPageBtn.onclick = () => this.navigatePdf(1);
            this.updatePdfNavigation();

            this.hideModalLoadingIndicator();
        } catch (err) {
            console.error('❌ Error en vista previa PDF:', err);
            this.hideModalLoadingIndicator();
            this.showErrorMessage(`No se pudo generar la vista previa: ${err.message}`);
        }
    }

    closePreviewModal() {
        console.log('Cerrando modal de previsualización...');
        
        // Ocultar modal inmediatamente (sin animación para evitar problemas)
        if (this.modal) {
            this.modal.style.display = 'none';
            this.modal.style.animation = '';
        }
        
        // Remover la clase modal-open del body
        document.body.classList.remove('modal-open');
        
        // Limpiar cualquier estilo inline que pueda haber sido aplicado al body
        if (document.body.hasAttribute('style')) {
            document.body.removeAttribute('style');
        }
        document.body.style.cssText = '';
        
        // CRÍTICO: Limpiar el contenedor de páginas de previsualización para liberar memoria
        if (this.previewPagesContainer) {
            this.previewPagesContainer.innerHTML = '';
        }
        
        // CRÍTICO: Limpiar datos de PDF
        this.currentPdfDoc = null;
        this.currentPdfBlob = null;
        this.totalPdfPages = 0;
        this.currentPdfPage = 0;
        
        // Limpiar indicadores de carga si existen
        this.hideModalLoadingIndicator();
        
        // Forzar el restablecimiento completo de estilos del body
        this.ensureCorrectBodyStyles();
        
        console.log('✅ Modal cerrado completamente y estilos restaurados');
    }

    showModalLoadingIndicator() {
        // Remover indicador existente si lo hay
        this.hideModalLoadingIndicator();
        
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'loading-indicator';
        loadingDiv.id = 'modalLoadingIndicator';
        loadingDiv.innerHTML = `
            <div class="loading-spinner"></div>
            <p style="margin: 0; font-weight: 600; color: #333;">Cargando previsualización...</p>
        `;
        
        this.previewPagesContainer.appendChild(loadingDiv);
    }

    hideModalLoadingIndicator() {
        const loadingIndicator = document.getElementById('modalLoadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.remove();
        }
    }

    showValidationError(missingFields) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'loading-indicator';
        errorDiv.style.background = 'rgba(220, 53, 69, 0.95)';
        errorDiv.style.color = 'white';
        errorDiv.innerHTML = `
            <div style="font-size: 48px;">⚠️</div>
            <h3 style="margin: 10px 0; color: white;">Campos Requeridos Faltantes</h3>
            <p style="margin: 0; color: white; text-align: center;">Por favor complete los siguientes campos:</p>
            <ul style="text-align: left; margin: 15px 0; padding-left: 20px; color: white;">
                ${missingFields.map(field => `<li>${field}</li>`).join('')}
            </ul>
            <button onclick="this.parentElement.remove()" style="
                background: white; 
                color: #dc3545; 
                border: none; 
                padding: 10px 20px; 
                border-radius: 5px; 
                font-weight: 600; 
                cursor: pointer;
                margin-top: 10px;
            ">Entendido</button>
        `;
        
        this.previewPagesContainer.appendChild(errorDiv);
        
        // Auto-cerrar después de 10 segundos
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 10000);
    }

    showErrorMessage(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'loading-indicator';
        errorDiv.style.background = 'rgba(220, 53, 69, 0.95)';
        errorDiv.style.color = 'white';
        errorDiv.innerHTML = `
            <div style="font-size: 48px;">❌</div>
            <h3 style="margin: 10px 0; color: white;">Error</h3>
            <p style="margin: 0; color: white; text-align: center;">${message}</p>
            <button onclick="this.parentElement.remove()" style="
                background: white; 
                color: #dc3545; 
                border: none; 
                padding: 10px 20px; 
                border-radius: 5px; 
                font-weight: 600; 
                cursor: pointer;
                margin-top: 15px;
            ">Cerrar</button>
        `;
        
        this.previewPagesContainer.appendChild(errorDiv);
    }















    async generateWordDocument() {
        try {
            // Mostrar indicador de carga
            this.showLoadingIndicator();

            // Recopilar datos del formulario
            const formData = this.collectFormData();

            // Validar campos requeridos
            const validation = this.validateRequiredFields(formData);
            if (!validation.isValid) {
                this.hideLoadingIndicator();
                alert(`Por favor complete los siguientes campos requeridos:\n${validation.missingFields.join('\n')}`);
                return;
            }

            // Crear FormData para enviar al servidor
            const submitData = new FormData();

            // Agregar datos del formulario
            Object.keys(formData).forEach(key => {
                submitData.append(key, formData[key]);
            });

            // Agregar imágenes con sus descripciones desde formData
            if (formData.image_files_data && formData.image_files_data.length > 0) {
                formData.image_files_data.forEach((imageData, index) => {
                    // Si tenemos el archivo original, agregarlo
                    if (imageData.originalFile) {
                        submitData.append('images', imageData.originalFile);
                    }
                    
                    // Agregar descripción
                    submitData.append(`image_description_${index}`, imageData.description || `Imagen ${index + 1}`);
                });
            }

            // Enviar al servidor
            const response = await fetch('/generate-bsi-pw1100', {
                method: 'POST',
                body: submitData
            });

            if (!response.ok) {
                throw new Error(`Error del servidor: ${response.status}`);
            }

            // Descargar el archivo
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `BSI_PW1100_Report_${new Date().toISOString().split('T')[0]}.docx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.hideLoadingIndicator();
            alert('Documento Word generado exitosamente');

        } catch (error) {
            this.hideLoadingIndicator();
            console.error('Error generando documento:', error);
            alert(`Error generando el documento: ${error.message}`);
        }
    }

    collectFormData() {
        const data = {};
        const formElements = this.form.elements;

        for (let i = 0; i < formElements.length; i++) {
            const element = formElements[i];
            if (element.name) {
                if (element.type === 'radio') {
                    if (element.checked) {
                        data[element.name] = element.value;
                    }
                } else if (element.type === 'checkbox') {
                    // Si hay checkboxes, su valor se manejaría aquí
                    data[element.name] = element.checked;
                } else if (element.tagName === 'SELECT') {
                    data[element.name] = element.value;
                } else if (element.type !== 'file') { // Excluir inputs de tipo file (imágenes se manejan aparte)
                    data[element.name] = element.value.trim();
                }
            }
        }

        // Recopilar datos de las imágenes organizadas por secciones
        const urlParams = new URLSearchParams(window.location.search);
        const mode = urlParams.get('edit') ? 'edit' : 'draft';
        const formId = urlParams.get('edit') || null;
        
        // Obtener imágenes organizadas por sección en el orden correcto
        const imagesBySections = window.imageManager.getImagesBySections(mode, formId);
        
        data.image_files_data = [];
        data.images_by_section = {}; // Estructura organizativa para el PDF
        let totalImages = 0;
        
        // Procesar imágenes manteniendo el orden por secciones
        Object.keys(imagesBySections).forEach(section => {
            const sectionImages = imagesBySections[section];
            
            if (sectionImages.length > 0) {
                console.log(`📊 Sección ${section}: ${sectionImages.length} imágenes`);
                data.images_by_section[section] = [];
                
                sectionImages.forEach((imageData, index) => {
                    totalImages++;
                    
                    const processedImage = {
                        id: imageData.id,
                        name: imageData.name,
                        size: imageData.size || 0,
                        type: imageData.type || 'image/jpeg',
                        description: imageData.description || `Imagen ${section} ${index + 1}`,
                        src: imageData.src, // Data URL para previsualización
                        s3_key: imageData.s3_key || null, // Key en S3
                        s3_url: imageData.s3_url || null, // URL en S3
                        uploaded: imageData.uploaded || false, // Si fue subida a S3
                        uploadedAt: imageData.uploadedAt || null, // Timestamp de subida
                        loaded: imageData.loaded || false, // Si se cargó desde S3
                        section: section // Mantener referencia de la sección
                    };
                    
                    // Agregar a ambas estructuras para compatibilidad
                    data.image_files_data.push(processedImage);
                    data.images_by_section[section].push(processedImage);
                    
                    console.log(`✅ Imagen ${totalImages}: ${imageData.name} [${section}] - S3: ${imageData.uploaded ? 'Sí' : 'No'}`);
                });
            }
        });
        
        console.log(`📊 Total de datos de imágenes preparados: ${totalImages} organizadas por secciones`);
        console.log(`📋 Secciones con imágenes: ${Object.keys(data.images_by_section).filter(s => data.images_by_section[s].length > 0).join(', ')}`);

        return data;
    }

    validateRequiredFields(formData) {
        const requiredFields = [
            { key: 'nombre_registrado', label: 'Nombre registrado' },
            { key: 'work_order_number', label: 'Work Order Number' },
            { key: 'date_of_bsi', label: 'Date of BSI' },
            { key: 'inspected_by', label: 'Inspected By' },
            { key: 'inspector_stamp', label: 'Inspector Stamp' },
            { key: 'station', label: 'Station' },
            { key: 'bsi_reason', label: 'BSI accomplished reason' },
            { key: 'bsi_type', label: 'Type of BSI' },
            { key: 'references_used', label: 'References Used' },
            { key: 'aircraft_model', label: 'Aircraft Model' },
            { key: 'engine_sn', label: 'Engine S/N' },
            { key: 'boroscope_type', label: 'Boroscope Used type' },
            { key: 'boroscope_sn', label: 'Boroscope S/N' },
            { key: 'probe_sn', label: 'Probe S/N' },
            { key: 'final_disposition', label: 'Final disposition' },
            { key: 'engine_status_bsi', label: 'Engine Status after BSI' },
            { key: 'new_interval_inspections', label: 'New Interval Inspections' },
            { key: 'user_email', label: 'Email' },
            { key: 'inspection_time', label: 'Inspection Time' },
            { key: 'interval_affected', label: 'Interval inspection Affected' }
        ];

        const missingFields = [];

        requiredFields.forEach(field => {
            if (!formData[field.key] || formData[field.key].trim() === '') {
                missingFields.push(`- ${field.label}`);
            }
        });

        return {
            isValid: missingFields.length === 0,
            missingFields
        };
    }

    showLoadingIndicator(message = 'Cargando...') {
        const wordButton = document.querySelector('.generate-word-btn');
        const previewButton = document.querySelector('.preview-word-btn');
        
        if (wordButton) {
            wordButton.disabled = true;
            wordButton.innerHTML = `⏳ ${message}`;
        }
        if (previewButton) {
            previewButton.disabled = true;
            previewButton.innerHTML = `⏳ ${message}`;
        }
    }

    hideLoadingIndicator() {
        const wordButton = document.querySelector('.generate-word-btn');
        const previewButton = document.querySelector('.preview-word-btn');

        if (wordButton) {
            wordButton.disabled = false;
            wordButton.innerHTML = '📄 Generar Documento Word';
        }
        if (previewButton) {
            previewButton.disabled = false;
            previewButton.innerHTML = '👁️ Previsualizar Documento';
        }
    }

    fillFormWithDebugData() {
        console.log('Llenando formulario con datos de debug...');
        
        // Datos de prueba para el formulario BSI PW1100
        const debugData = {
            // Información general
            nombre_registrado: 'Inspector de Prueba',
            work_order_number: 'WO-2024-001234',
            date_of_bsi: '2024-01-15',
            inspected_by: 'Julio Acosta',
            inspector_stamp: 'QC-003',
            station: 'MTY',
            bsi_reason: 'Maintenance Program',
            bsi_type: 'Full BSI',
            references_used: 'AMM 72-00-00, SB PW1100G-72-001',
            
            // Información de aeronave
            aircraft_model: 'A320 NEO',
            aircraft_registration: 'XA-VBA',
            engine_sn: 'PW123456789',
            
            // Equipo utilizado
            boroscope_type: 'Mentor IQ',
            boroscope_sn: '1830A9916',
            probe_sn: '1602A1890',
            
            // LPC Stages
            lpc_stage1_status: 'No Damage Found',
            lpc_stage1_remarks: 'Inspección visual completada sin anomalías detectadas.',
            lpc_stage2_status: 'Damage In Limits',
            lpc_stage2_remarks: 'Desgaste menor observado en álabes, dentro de límites aceptables.',
            lpc_stage3_status: 'No Damage Found',
            lpc_stage3_remarks: 'Estado satisfactorio.',
            
            // #3 Bearing
            bearing3_front_status: 'No Damage Found',
            bearing3_front_remarks: 'Sello frontal en condiciones normales.',
            bearing3_rear_status: 'No Damage Found',
            bearing3_rear_remarks: 'Sello trasero sin evidencia de fugas.',
            
            // HPC Stages (ejemplos para algunos)
            hpc_stage1_status: 'No Damage Found',
            hpc_stage1_remarks: 'Álabes en condición satisfactoria.',
            hpc_stage2_status: 'Damage In Limits',
            hpc_stage2_remarks: 'Erosión menor en leading edge, dentro de límites.',
            hpc_stage3_status: 'No Damage Found',
            hpc_stage3_remarks: 'Sin anomalías detectadas.',
            hpc_stage4_status: 'No Damage Found',
            hpc_stage4_remarks: 'Estado normal.',
            hpc_stage5_status: 'No Damage Found',
            hpc_stage5_remarks: 'Inspección satisfactoria.',
            hpc_stage6_status: 'No Damage Found',
            hpc_stage6_remarks: 'Sin daños visibles.',
            hpc_stage7_status: 'No Damage Found',
            hpc_stage7_remarks: 'Condición aceptable.',
            hpc_stage8_status: 'No Damage Found',
            hpc_stage8_remarks: 'Última etapa en buen estado.',
            
            // Combustion Chamber
            igniter_status: 'No Damage Found',
            igniter_remarks: 'Segmento del encendedor en condiciones normales.',
            fuelnozzle_status: 'Damage In Limits',
            fuelnozzle_remarks: 'Depósitos de carbón menores, limpieza recomendada.',
            cch_inner_status: 'No Damage Found',
            cch_inner_remarks: 'Liner interno sin grietas o deformaciones.',
            cch_outer_status: 'No Damage Found',
            cch_outer_remarks: 'Liner externo en condición satisfactoria.',
            
            // Ship Lap
            shiplap_status: 'Damage In Limits',
            shiplap_remarks: 'Desgaste normal por operación, dentro de tolerancias.',
            shiplap_dimensions: '2.5mm gap measured',
            
            // HPT
            hpt_vane_status: 'No Damage Found',
            hpt_vane_remarks: 'Álabes guía en condición normal.',
            hpt_s1_status: 'Damage In Limits',
            hpt_s1_remarks: 'Erosión térmica menor en trailing edge.',
            hpt_s2_status: 'No Damage Found',
            hpt_s2_remarks: 'Segunda etapa sin anomalías.',
            
            // LPT
            lpt_s1_status: 'No Damage Found',
            lpt_s1_remarks: 'Primera etapa LPT en buen estado.',
            lpt_s2_status: 'No Damage Found',
            lpt_s2_remarks: 'Segunda etapa sin daños.',
            lpt_s3_status: 'No Damage Found',
            lpt_s3_remarks: 'Tercera etapa satisfactoria.',
            
            // Disposición final
            final_disposition: 'Motor aprobado para operación continua. Se recomienda limpieza de inyectores de combustible en próximo mantenimiento programado. Todos los hallazgos están dentro de límites aceptables según manual de mantenimiento.',
            engine_status_bsi: 'Serviceable to Operation',
            new_interval_inspections: '6000 FC / 12000 FH',
            user_email: '<EMAIL>',
            inspection_time: '180',
            
            // Intervalos
            interval_affected: 'No',
            interval_next_fc: '6000',
            interval_next_fh: '12000'
        };
        
        // Llenar todos los campos del formulario
        Object.keys(debugData).forEach(fieldName => {
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                if (field.type === 'radio') {
                    // Para radio buttons, buscar el que coincida con el valor
                    const radioButton = this.form.querySelector(`[name="${fieldName}"][value="${debugData[fieldName]}"]`);
                    if (radioButton) {
                        radioButton.checked = true;
                    }
                } else if (field.type === 'checkbox') {
                    field.checked = debugData[fieldName];
                } else {
                    // Para inputs normales, textareas y selects
                    field.value = debugData[fieldName];
                }
            }
        });
        
        console.log('Formulario llenado con datos de debug');
        
        // Mostrar notificación visual
        this.showDebugNotification();
    }

    showDebugNotification() {
        // Crear notificación temporal
        const notification = document.createElement('div');
        notification.innerHTML = '✅ Formulario llenado con datos de prueba';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #4caf50;
            color: white;
            padding: 15px 20px;
            border-radius: 4px;
            font-weight: 600;
            z-index: 10000;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            animation: slideIn 0.3s ease-out;
        `;
        
        // Agregar animación CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(notification);
        
        // Remover después de 3 segundos
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                if (style.parentNode) {
                    style.parentNode.removeChild(style);
                }
            }, 300);
        }, 3000);
    }

    clearForm() {
        console.log('Limpiando formulario...');
        
        // Limpiar todos los inputs de texto, textareas, selects
        const textInputs = this.form.querySelectorAll('input[type="text"], input[type="email"], input[type="number"], input[type="date"], textarea, select');
        textInputs.forEach(input => {
            input.value = '';
        });
        
        // Limpiar radio buttons
        const radioInputs = this.form.querySelectorAll('input[type="radio"]');
        radioInputs.forEach(radio => {
            radio.checked = false;
        });
        
        // Limpiar checkboxes
        const checkboxInputs = this.form.querySelectorAll('input[type="checkbox"]');
        checkboxInputs.forEach(checkbox => {
            checkbox.checked = false;
        });
        
        // Limpiar preview de imágenes
        const imagePreview = document.getElementById('image-preview');
        if (imagePreview) {
            imagePreview.innerHTML = '';
        }
        
        // Limpiar archivos de imagen
        const imageInput = document.getElementById('inspection_images');
        if (imageInput) {
            imageInput.value = '';
        }
        
        // Limpiar imágenes del storage
        const mode = this.isEditMode ? 'edit' : 'draft';
        const formId = this.isEditMode ? this.originalFormId : null;
        window.imageManager.clearStorage(mode, formId);
        
        this.imageFiles = [];
        
        console.log('Formulario limpiado');
        
        // Mostrar notificación
        this.showClearNotification();
    }

    showClearNotification() {
        // Crear notificación temporal
        const notification = document.createElement('div');
        notification.innerHTML = '🗑️ Formulario eliminado';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #6c757d;
            color: white;
            padding: 15px 20px;
            border-radius: 4px;
            font-weight: 600;
            z-index: 10000;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            animation: slideIn 0.3s ease-out;
        `;
        
        document.body.appendChild(notification);
        
        // Remover después de 2 segundos
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2000);
    }





    removeAllImages() {
        // Determinar modo y formId según URL
        const urlParams = new URLSearchParams(window.location.search);
        const mode = urlParams.get('edit') ? 'edit' : 'draft';
        const formId = urlParams.get('edit') || null;
        
        // Obtener imágenes actuales para verificar si hay algo que eliminar
        const currentImages = window.imageManager.getImagesWithDescriptions(mode, formId);
        
        // Confirmar antes de eliminar todas las imágenes
        if (currentImages.length > 0 && confirm('¿Está seguro de que desea eliminar todas las imágenes?')) {
            // Limpiar del localStorage
            window.imageManager.clearStorage(mode, formId);
            
            // Actualizar previsualización
            this.refreshImagePreview();
            
            // Limpiar también el input de archivos
            const imageInput = document.getElementById('inspection_images');
            if (imageInput) {
                imageInput.value = '';
            }
            
            // Limpiar array local para compatibilidad
            this.imageFiles = [];
            
            console.log('✅ Todas las imágenes han sido eliminadas');
        }
    }

    /**
     * Cargar imágenes guardadas al iniciar el formulario
     */
    async loadSavedImages() {
        try {
            // Determinar modo y formId según URL
            const urlParams = new URLSearchParams(window.location.search);
            const mode = urlParams.get('edit') ? 'edit' : 'draft';
            const formId = urlParams.get('edit') || null;
            
            // Cargar imágenes desde localStorage
            const savedImages = await window.imageManager.loadFromStorage(mode, formId);
            
            if (savedImages.length > 0) {
                console.log(`📸 Cargando ${savedImages.length} imágenes guardadas...`);
                
                // Mostrar previsualización (ImageManager ya maneja los datos)
                this.refreshImagePreview();
                
                console.log('✅ Imágenes cargadas correctamente');
            } else {
                console.log('📸 No hay imágenes guardadas para cargar');
            }
            
        } catch (error) {
            console.error('❌ Error cargando imágenes guardadas:', error);
        }
    }

    // Métodos para guardar y cargar datos del formulario en localStorage
    saveFormData(data) {
        try {
            const mode = this.isEditMode ? 'edit' : 'draft';
            const formId = this.isEditMode ? this.originalFormId : null;
            
            window.localStorageManager.saveFormData(data, mode, formId);
            
            // Las imágenes se guardan automáticamente por ImageManager cuando se procesan
            
            console.log(`💾 Datos del formulario guardados en localStorage [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}]`);
        } catch (e) {
            console.error('Error al guardar datos en localStorage:', e);
        }
    }

    async loadFormData() {
        // Si estamos en modo edición, no cargar desde localStorage
        if (this.isEditMode) {
            console.log('🔄 Modo edición: omitiendo carga desde localStorage');
            return;
        }
        
        try {
            const mode = 'draft';
            const formId = null;
            
            // Cargar datos del formulario
            const formData = window.localStorageManager.loadFormData(mode, formId);
            
            if (formData) {
                // Iterar sobre los datos y rellenar el formulario
                for (const key in formData) {
                    const input = this.form.elements[key];
                    if (input) {
                        if (input.type === 'radio') {
                            const radio = this.form.querySelector(`input[name="${key}"][value="${formData[key]}"]`);
                            if (radio) radio.checked = true;
                        } else if (input.type === 'checkbox') {
                            input.checked = formData[key];
                        } else {
                            input.value = formData[key];
                        }
                    }
                }
                
                console.log('📥 Datos del formulario cargados desde localStorage');
            }
            
            // Cargar imágenes por separado
            const images = await window.imageManager.loadFromStorage(mode, formId);
            
            if (images && images.length > 0) {
                this.refreshImagePreview();
                
                // Rellenar descripciones de imágenes después de que se muestren
                setTimeout(() => {
                    images.forEach((imgData) => {
                        const textarea = document.getElementById(`image_description_${imgData.id}`);
                        if (textarea) {
                            textarea.value = imgData.description || '';
                        }
                    });
                }, 500);
                
                console.log(`📸 ${images.length} imagen(es) cargada(s) desde localStorage`);
            }
            
        } catch (e) {
            console.error('Error al cargar datos desde localStorage:', e);
        }
    }



    // ===== NUEVOS MÉTODOS PARA NAVEGACIÓN CON PDF.js =====
    async renderCurrentPdfPage(){
        if(!this.currentPdfDoc){
            console.warn('No hay documento PDF cargado');
            return;
        }
        try{
            const wrapper=document.getElementById('pdfWrapper');
            let spinner=document.createElement('div');
            spinner.className='page-spinner';
            wrapper.appendChild(spinner);
 
            const page = await this.currentPdfDoc.getPage(this.currentPdfPage);
            const wrapperWidth = wrapper.clientWidth || 800;
            const viewport0 = page.getViewport({ scale: 1.5 });
            const fitScale = wrapperWidth / viewport0.width;
            const dpr = window.devicePixelRatio || 1;
            const renderScale = fitScale * this.currentScale * dpr;
            const viewport = page.getViewport({ scale: renderScale });
 
            // Off-screen canvas para evitar pintado progresivo visible
            const offCanvas = document.createElement('canvas');
            offCanvas.width = viewport.width;
            offCanvas.height = viewport.height;
            const offCtx = offCanvas.getContext('2d');
 
            await page.render({ canvasContext: offCtx, viewport }).promise;
 
            const canvas = document.getElementById('pdfCanvas');
            canvas.width = viewport.width;
            canvas.height = viewport.height;
            canvas.style.width = (viewport.width / dpr) + 'px';
            canvas.style.height = (viewport.height / dpr) + 'px';
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0,0,canvas.width,canvas.height);
            ctx.drawImage(offCanvas,0,0);
 
            spinner.remove();
        }catch(err){
            console.error('Error renderizando página PDF:', err);
        }
    }

    navigatePdf(delta){
        if(!this.currentPdfDoc){return;}
        const newPage = this.currentPdfPage + delta;
        if(newPage < 1 || newPage > this.totalPdfPages){return;}
        this.currentPdfPage = newPage;
        this.renderCurrentPdfPage();
        this.updatePdfNavigation();
    }

    updatePdfNavigation(){
        if(!this.pageNumberDisplay){return;}
        this.pageNumberDisplay.textContent = `Página ${this.currentPdfPage} de ${this.totalPdfPages}`;
        if(this.prevPageBtn){ this.prevPageBtn.disabled = this.currentPdfPage === 1; }
        if(this.nextPageBtn){ this.nextPageBtn.disabled = this.currentPdfPage === this.totalPdfPages; }
    }

    // Crea wrapper y canvas si no existen (tras cerrar modal se eliminan)
    ensurePdfElements(){
        if(!this.previewPagesContainer){return;}
        let wrapper=document.getElementById('pdfWrapper');
        if(!wrapper){
            wrapper=document.createElement('div');
            wrapper.id='pdfWrapper';
            wrapper.style.maxWidth='90%';
            wrapper.style.width='90%';
            wrapper.style.margin='0 auto';
            wrapper.style.maxHeight='75vh';
            wrapper.style.overflow='auto';
            wrapper.style.position='relative';
            wrapper.style.padding='10px';
            this.previewPagesContainer.appendChild(wrapper);
        }
        if(!document.getElementById('pdfCanvas')){
            const canvas=document.createElement('canvas');
            canvas.id='pdfCanvas';
            canvas.style.width='100%';
            canvas.style.height='auto';
            wrapper.appendChild(canvas);
        }
    }

    // ====================================================
    // MÉTODOS PARA EDICIÓN DE FORMULARIOS ENVIADOS
    // ====================================================

    checkForEditMode() {
        console.log('🔍 Verificando modo de edición...');
        
        // Obtener parámetros de la URL
        const urlParams = new URLSearchParams(window.location.search);
        const editFormId = urlParams.get('edit');
        
        if (editFormId) {
            console.log(`✏️ Modo edición detectado para formulario: ${editFormId}`);
            this.isEditMode = true;
            this.originalFormId = editFormId;
            this.showEditBanner();
            this.loadSubmittedFormData(editFormId);
        } else {
            console.log('📝 Modo creación normal');
            this.isEditMode = false;
        }
    }

    showEditBanner() {
        console.log('🎨 Mostrando banner de edición...');
        
        // Crear banner si no existe
        let banner = document.getElementById('editing-banner');
        if (!banner) {
            banner = document.createElement('div');
            banner.id = 'editing-banner';
            banner.className = 'editing-mode-banner';
            banner.innerHTML = `
                <div class="banner-content">
                    <div class="banner-text">
                        <i class="fas fa-edit"></i>
                        <span>Editando formulario enviado - ID: <strong>${this.originalFormId.substring(0, 8)}...</strong></span>
                    </div>
                    <button class="btn-cancel-edit" onclick="handler.cancelEdit()">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                </div>
            `;
            
            // Insertar banner al inicio del formulario
            const formContainer = document.querySelector('.form-container');
            if (formContainer) {
                formContainer.insertBefore(banner, formContainer.firstChild);
            }
        }
        
        banner.style.display = 'block';
        
        // Agregar estilos para el banner
        if (!document.getElementById('edit-banner-styles')) {
            const style = document.createElement('style');
            style.id = 'edit-banner-styles';
            style.textContent = `
                .editing-mode-banner {
                    background: linear-gradient(135deg, #007bff, #0056b3);
                    color: white;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 20px;
                    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
                }
                .banner-content {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .banner-text {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    font-weight: 500;
                }
                .btn-cancel-edit {
                    background: rgba(255, 255, 255, 0.2);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    color: white;
                    padding: 8px 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 5px;
                }
                .btn-cancel-edit:hover {
                    background: rgba(255, 255, 255, 0.3);
                    transform: translateY(-1px);
                }
                @media (max-width: 768px) {
                    .banner-content {
                        flex-direction: column;
                        gap: 15px;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    async loadSubmittedFormData(formId) {
        console.log(`📥 Cargando datos del formulario enviado: ${formId}`);
        
        try {
            // Mostrar loading
            this.showLoadingIndicator('Cargando formulario enviado...');
            
            const response = await fetch(`/api/submitted-forms/${formId}`);
            const data = await response.json();
            
            if (data.success) {
                console.log('✅ Formulario enviado cargado exitosamente');
                this.editingFormData = data.form.form_data;
                
                // Actualizar el banner con información del formulario
                this.updateEditBanner(data.form);
                
                // Poblar el formulario con los datos (similar a loadFormData pero desde BD)
                this.populateFormWithSubmittedData(data.form.form_data);
                
            } else {
                console.error('❌ Error cargando formulario enviado:', data.error);
                this.showErrorMessage('Error cargando formulario enviado');
                this.cancelEdit();
            }
            
        } catch (error) {
            console.error('❌ Error de conexión:', error);
            this.showErrorMessage('Error de conexión al cargar formulario');
            this.cancelEdit();
        } finally {
            this.hideLoadingIndicator();
        }
    }

    updateEditBanner(formData) {
        const banner = document.getElementById('editing-banner');
        if (banner && formData.form_data) {
            const bannerText = banner.querySelector('.banner-text span');
            if (bannerText) {
                bannerText.innerHTML = `
                    Editando formulario enviado - WO: <strong>${formData.form_data.work_order_number || 'N/A'}</strong>
                `;
            }
        }
    }

    async populateFormWithSubmittedData(formData) {
        console.log('🔄 Poblando formulario con datos enviados...');
        
        try {
            // Iterar sobre los datos y rellenar el formulario
            for (const key in formData) {
                const input = this.form.elements[key];
                if (input) {
                    if (input.type === 'radio') {
                        const radio = this.form.querySelector(`input[name="${key}"][value="${formData[key]}"]`);
                        if (radio) radio.checked = true;
                    } else if (input.type === 'checkbox') {
                        input.checked = formData[key];
                    } else {
                        input.value = formData[key];
                    }
                }
            }
            
            // Manejar la carga de imágenes desde formulario enviado
            if (formData.image_files_data && Array.isArray(formData.image_files_data)) {
                console.log(`📸 Cargando ${formData.image_files_data.length} imágenes desde formulario enviado...`);
                
                this.showLoadingIndicator('Cargando imágenes...');
                
                try {
                    // Usar ImageManager para cargar imágenes desde formulario enviado
                    const loadedImages = await window.imageManager.loadFromSubmittedForm(formData.image_files_data);
                    
                    this.refreshImagePreview();
                    
                    // Rellenar descripciones de imágenes después de que se muestren
                    setTimeout(() => {
                        loadedImages.forEach((imgData) => {
                            const textarea = document.getElementById(`image_description_${imgData.id}`);
                            if (textarea) {
                                textarea.value = imgData.description || '';
                            }
                        });
                    }, 500);
                    
                    console.log(`✅ ${loadedImages.length} imagen(es) cargada(s) desde formulario enviado`);
                    
                } catch (imageError) {
                    console.error('❌ Error cargando imágenes:', imageError);
                    this.showErrorMessage('Error cargando imágenes del formulario');
                } finally {
                    this.hideLoadingIndicator();
                }
            }
            
            console.log('✅ Formulario poblado exitosamente con datos enviados');
            
        } catch (error) {
            console.error('❌ Error poblando formulario:', error);
            this.showErrorMessage('Error cargando datos del formulario');
        }
    }

    cancelEdit() {
        console.log('❌ Cancelando edición...');
        
        // Confirmar cancelación
        if (confirm('¿Estás seguro de que quieres cancelar la edición? Se perderán los cambios no guardados.')) {
            // Volver a la página de formularios enviados
            window.location.href = '../index.html';
        }
    }

    // Modificar el método collectFormData para incluir información de edición
    collectFormDataWithEditInfo() {
        const formData = this.collectFormData();
        
        if (this.isEditMode) {
            formData._isEdit = true;
            formData._originalFormId = this.originalFormId;
            formData._editTimestamp = new Date().toISOString();
        }
        
        return formData;
    }

    showSuccessMessage(message) {
        const div = document.createElement('div');
        div.textContent = message;
        div.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            font-weight: 600;
            max-width: 400px;
        `;
        document.body.appendChild(div);
        setTimeout(() => div.remove(), 5000);
    }
}

// Inicializar el manejador cuando se carga la página
window.bsiHandler = new BSI_PW1100_Handler();
// Para compatibilidad con diagnósticos y scripts existentes
window.handler = window.bsiHandler;

// ====================================================
// FUNCIONES GLOBALES PARA BOTONES
// ====================================================

// Función global para llenar datos de prueba
window.fillTestData = function() {
    console.log('🔧 Ejecutando fillTestData...');
    if (window.handler && typeof window.handler.fillFormWithDebugData === 'function') {
        window.handler.fillFormWithDebugData();
    } else if (window.EmergencyDiagnostic && typeof window.EmergencyDiagnostic.fillTestData === 'function') {
        window.EmergencyDiagnostic.fillTestData();
    } else {
        console.error('❌ fillFormWithDebugData no encontrado en handler');
        // Fallback básico
        alert('Función fillTestData no disponible. Verifica la consola.');
    }
};

// Función global para previsualizar PDF
window.previewPdf = function() {
    console.log('🔧 Ejecutando previewPdf...');
    if (window.handler && typeof window.handler.openPreviewModal === 'function') {
        window.handler.openPreviewModal();
    } else {
        console.error('❌ openPreviewModal no encontrado en handler');
        // Fallback básico
        alert('Función previewPdf no disponible. Verifica la consola.');
    }
};

console.log('✅ Funciones globales fillTestData y previewPdf disponibles');