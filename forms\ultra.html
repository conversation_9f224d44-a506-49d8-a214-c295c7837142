<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultrasonic Test Form (NDT-009, Rev 1)</title>
    <link rel="stylesheet" href="../css/forms-common.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script  src="../js/select-search.js"></script>
</head>
<body>
    <header class="header">
        <a href="../index.html" style="text-decoration: none;">
            <img src="../img/Viva_Logo.svg.png" alt="Vivaaerobus Logo" class="logo">
        </a>
        <div class="user-profile">
            <span>Inspector A</span>
            <div class="user-icon">
                <i class="fas fa-user" style="color: white;"></i>
            </div>
        </div>
    </header>

    <div class="form-container">
        <h1>Ultrasonic Test Form (NDT-009, Rev 1)</h1>
        <p class="form-note header-note">(Copia)</p>
        <p class="form-note"><span class="asterisk">*</span> Obligatoria</p>

        <form action="#" method="post">

            <div class="form-group">
                <label for="nombre_registrado"><span class="asterisk">*</span> Este formulario registrará su nombre, escriba su nombre.</label>
                <input type="text" id="nombre_registrado" name="nombre_registrado" required>
            </div>

            <fieldset>
                <legend>General Information</legend>

                <div class="form-group">
                    <label for="ut_work_order">1. UT Work Order <span class="asterisk">*</span></label>
                    <input type="text" id="ut_work_order" name="ut_work_order" required>
                </div>

                <div class="form-group">
                    <label for="ac_registration">2. A/C Registration <span class="asterisk">*</span></label>
                    <select id="ac_registration" name="ac_registration" required>
                        <option value="" disabled selected>Seleccione una matrícula</option>
                        <option value="XA-VAA">XA-VAA</option>
                        <option value="XA-VAB">XA-VAB</option>
                        <option value="XA-VAC">XA-VAC</option>
                        <option value="XA-VAE">XA-VAE</option>
                        <option value="XA-VAI">XA-VAI</option>
                        <option value="XA-VAJ">XA-VAJ</option>
                        <option value="XA-VAK">XA-VAK</option>
                        <option value="XA-VAM">XA-VAM</option>
                        <option value="XA-VAN">XA-VAN</option>
                        <option value="XA-VAO">XA-VAO</option>
                        <option value="XA-VAP">XA-VAP</option>
                        <option value="XA-VAQ">XA-VAQ</option>
                        <option value="XA-VAR">XA-VAR</option>
                        <option value="XA-VAT">XA-VAT</option>
                        <option value="XA-VAU">XA-VAU</option>
                        <option value="XA-VAV">XA-VAV</option>
                        <option value="XA-VAW">XA-VAW</option>
                        <option value="XA-VAX">XA-VAX</option>
                        <option value="XA-VAY">XA-VAY</option>
                        <option value="XA-VBA">XA-VBA</option>
                        <option value="XA-VBB">XA-VBB</option>
                        <option value="XA-VBI">XA-VBI</option>
                        <option value="XA-VBH">XA-VBH</option>
                        <option value="XA-VBJ">XA-VBJ</option>
                        <option value="XA-VBK">XA-VBK</option>
                        <option value="XA-VBM">XA-VBM</option>
                        <option value="XA-VBN">XA-VBN</option>
                        <option value="XA-VBP">XA-VBP</option>
                        <option value="XA-VBQ">XA-VBQ</option>
                        <option value="XA-VBR">XA-VBR</option>
                        <option value="XA-VBS">XA-VBS</option>
                        <option value="XA-VBT">XA-VBT</option>
                        <option value="XA-VBU">XA-VBU</option>
                        <option value="XA-VBV">XA-VBV</option>
                        <option value="XA-VBW">XA-VBW</option>
                        <option value="XA-VBX">XA-VBX</option>
                        <option value="XA-VBZ">XA-VBZ</option>
                        <option value="XA-CCC">XA-CCC</option>
                        <option value="XA-VCC">XA-VCC</option>
                        <option value="XA-VIA">XA-VIA</option>
                        <option value="XA-VIB">XA-VIB</option>
                        <option value="XA-VIE">XA-VIE</option>
                        <option value="XA-VIF">XA-VIF</option>
                        <option value="XA-VIH">XA-VIH</option>
                        <option value="XA-VII">XA-VII</option>
                        <option value="XA-VIJ">XA-VIJ</option>
                        <option value="XA-VIK">XA-VIK</option>
                        <option value="XA-VIL">XA-VIL</option>
                        <option value="XA-VIM">XA-VIM</option>
                        <option value="XA-VIN">XA-VIN</option>
                        <option value="XA-VIO">XA-VIO</option>
                        <option value="XA-VIP">XA-VIP</option>
                        <option value="XA-VIQ">XA-VIQ</option>
                        <option value="XA-VIR">XA-VIR</option>
                        <option value="XA-VIS">XA-VIS</option>
                        <option value="XA-VIT">XA-VIT</option>
                        <option value="XA-VIU">XA-VIU</option>
                        <option value="XA-VIV">XA-VIV</option>
                        <option value="XA-VIW">XA-VIW</option>
                        <option value="XA-VIX">XA-VIX</option>
                        <option value="XA-VIY">XA-VIY</option>
                        <option value="XA-VXA">XA-VXA</option>
                        <option value="XA-VXB">XA-VXB</option>
                        <option value="XA-VXC">XA-VXC</option>
                        <option value="XA-VXD">XA-VXD</option>
                        <option value="XA-VXE">XA-VXE</option>
                        <option value="XA-VXF">XA-VXF</option>
                        <option value="XA-VXG">XA-VXG</option>
                        <option value="XA-VXH">XA-VXH</option>
                        <option value="XA-VXI">XA-VXI</option>
                        <option value="XA-VXJ">XA-VXJ</option>
                        <option value="XA-VXK">XA-VXK</option>
                        <option value="XA-VXL">XA-VXL</option>
                        <option value="XA-VXM">XA-VXM</option>
                        <option value="XA-VXO">XA-VXO</option>
                        <option value="XA-VXP">XA-VXP</option>
                        <option value="XA-VYA">XA-VYA</option>
                        <option value="XA-VYB">XA-VYB</option>
                        <option value="XA-VYD">XA-VYD</option>
                        <option value="XA-VYE">XA-VYE</option>
                        <option value="XA-VYF">XA-VYF</option>
                        <option value="N/A">N/A</option>
                        <option value="XA-VMD">XA-VMD</option>
                    </select>
                </div>

                <div class="form-group">
                    <p class="radio-group-label">3. Station <span class="asterisk">*</span></p>
                    <div class="radio-group">
                        <div><input type="radio" id="station_mty" name="station" value="MTY" required><label for="station_mty">MTY</label></div>
                        <div><input type="radio" id="station_mex" name="station" value="MEX"><label for="station_mex">MEX</label></div>
                        <div><input type="radio" id="station_gdl" name="station" value="GDL"><label for="station_gdl">GDL</label></div>
                        <div><input type="radio" id="station_cun" name="station" value="CUN"><label for="station_cun">CUN</label></div>
                        <div><input type="radio" id="station_tij" name="station" value="TIJ"><label for="station_tij">TIJ</label></div>
                        <div><input type="radio" id="station_other" name="station" value="Other"><label for="station_other">Other</label></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="part_number_if_apply">4. Part Number (If Apply) <span class="asterisk">*</span></label>
                    <input type="text" id="part_number_if_apply" name="part_number_if_apply" required>
                </div>

                <div class="form-group">
                    <label for="date">5. Date <span class="asterisk">*</span></label>
                    <input type="date" id="date" name="date" required>
                </div>
            </fieldset>

            <fieldset>
                <legend>Equipment Used</legend>
                <div class="form-group">
                    <p class="radio-group-label">6. UT Manufacturer EQ <span class="asterisk">*</span></p>
                    <div class="radio-group">
                        <div><input type="radio" id="ut_manufacturer_olympus" name="ut_manufacturer_eq" value="Olympus" required><label for="ut_manufacturer_olympus">Olympus</label></div>
                        <div><input type="radio" id="ut_manufacturer_testia" name="ut_manufacturer_eq" value="Testia"><label for="ut_manufacturer_testia">Testia</label></div>
                    </div>
                </div>

                <div class="form-group">
                    <p class="radio-group-label">7. UT Part Number EQ <span class="asterisk">*</span></p>
                    <div class="radio-group">
                        <div><input type="radio" id="ut_part_eq_epoch4plus" name="ut_part_number_eq" value="EPOCH4PLUS" required><label for="ut_part_eq_epoch4plus">EPOCH4PLUS</label></div>
                        <div><input type="radio" id="ut_part_eq_epltc_ues" name="ut_part_number_eq" value="EPLTC-UES"><label for="ut_part_eq_epltc_ues">EPLTC-UES</label></div>
                        <div><input type="radio" id="ut_part_eq_omniscan_sx" name="ut_part_number_eq" value="OMNISCAN SX"><label for="ut_part_eq_omniscan_sx">OMNISCAN SX</label></div>
                        <div><input type="radio" id="ut_part_eq_te_ue1kit" name="ut_part_number_eq" value="TE-UE1KIT-D-B"><label for="ut_part_eq_te_ue1kit">TE-UE1KIT-D-B</label></div>
                        <div><input type="radio" id="ut_part_eq_99d5" name="ut_part_number_eq" value="99D51103005000"><label for="ut_part_eq_99d5">99D51103005000</label></div>
                        <div><input type="radio" id="ut_part_eq_ndtem_me" name="ut_part_number_eq" value="NDTEM-ME"><label for="ut_part_eq_ndtem_me">NDTEM-ME</label></div>
                        <div><input type="radio" id="ut_part_eq_72dlp" name="ut_part_number_eq" value="72DLP-P-UE"><label for="ut_part_eq_72dlp">72DLP-P-UE</label></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="ut_serial_number_eq">8. UT Serial Number Eq <span class="asterisk">*</span></label>
                    <select id="ut_serial_number_eq" name="ut_serial_number_eq" required>
                        <option value="" disabled selected>Seleccione un número de serie</option>
                        <option value="160196305">160196305</option>
                        <option value="160197605">160197605</option>
                        <option value="110248512">110248512</option>
                        <option value="QC-017267">QC-017267</option>
                        <option value="CLAD-190125-001">CLAD-190125-001</option>
                        <option value="TT-200002">TT-200002</option>
                        <option value="ME-005">ME-005</option>
                        <option value="221526412">221526412</option>
                        <option value="UE1N-220038">UE1N-220038</option>
                        <option value="221527612">221527612</option>
                        <option value="230013106">230013106</option>
                        <option value="230015409">230015409</option>
                        <option value="241957803">241957803</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="ut_calibration_due_date_eq">9. UT Calibration Due Date EQ <span class="asterisk">*</span></label>
                    <input type="date" id="ut_calibration_due_date_eq" name="ut_calibration_due_date_eq" required>
                </div>
            </fieldset>

            <fieldset>
                <legend>Transducer</legend>
                <div class="form-group">
                    <label for="ut_manufacturer_transducer">10. UT Manufacturer Transducer <span class="asterisk">*</span></label>
                    <input type="text" id="ut_manufacturer_transducer" name="ut_manufacturer_transducer" required>
                </div>
                <div class="form-group">
                    <label for="ut_part_number_transducer">11. UT Part Number Transducer <span class="asterisk">*</span></label>
                    <input type="text" id="ut_part_number_transducer" name="ut_part_number_transducer" required>
                </div>
                <div class="form-group">
                    <label for="ut_serial_number_transducer">12. UT Serial Number Transducer <span class="asterisk">*</span></label>
                    <input type="text" id="ut_serial_number_transducer" name="ut_serial_number_transducer" required>
                </div>
                <div class="form-group">
                    <label for="ut_frequency_transducer">13. UT Frequency Transducer <span class="asterisk">*</span></label>
                    <input type="text" id="ut_frequency_transducer" name="ut_frequency_transducer" required>
                </div>
            </fieldset>

            <fieldset>
                <legend>Reference Standard</legend>
                <div class="form-group">
                    <label for="ut_manufacturer_rs">14. UT Manufacturer RS <span class="asterisk">*</span></label>
                    <input type="text" id="ut_manufacturer_rs" name="ut_manufacturer_rs" required>
                </div>
                <div class="form-group">
                    <label for="ut_part_number_rs">15. UT Part Number RS <span class="asterisk">*</span></label>
                    <input type="text" id="ut_part_number_rs" name="ut_part_number_rs" required>
                </div>
                <div class="form-group">
                    <label for="ut_serial_number_rs">16. UT Serial Number RS <span class="asterisk">*</span></label>
                    <input type="text" id="ut_serial_number_rs" name="ut_serial_number_rs" required>
                </div>
                <div class="form-group">
                    <label for="ut_due_date_rs">17. UT Due Date RS</label>
                    <input type="date" id="ut_due_date_rs" name="ut_due_date_rs">
                </div>
                <div class="form-group">
                    <label for="ut_material_rs">18. UT Material RS <span class="asterisk">*</span></label>
                    <input type="text" id="ut_material_rs" name="ut_material_rs" required>
                </div>
            </fieldset>

            <fieldset>
                <legend>Inspection Data</legend>
                <div class="form-group">
                    <label for="ut_frequency_inspection">19. UT Frequency <span class="asterisk">*</span></label>
                    <input type="text" id="ut_frequency_inspection" name="ut_frequency_inspection" required>
                </div>
                <div class="form-group">
                    <label for="ut_gain">20. UT Gain <span class="asterisk">*</span></label>
                    <input type="text" id="ut_gain" name="ut_gain" required>
                </div>
                <div class="form-group">
                    <label for="ut_filters">21. UT Filters <span class="asterisk">*</span></label>
                    <input type="text" id="ut_filters" name="ut_filters" required>
                </div>
            </fieldset>

            <fieldset>
                <legend>Results of inspection</legend>
                <div class="form-group">
                    <p class="radio-group-label">22. Indication <span class="asterisk">*</span></p>
                    <div class="radio-group">
                        <div><input type="radio" id="indication_yes" name="indication" value="Yes" required><label for="indication_yes">Yes</label></div>
                        <div><input type="radio" id="indication_no" name="indication" value="No"><label for="indication_no">No</label></div>
                    </div>
                </div>
                <div class="form-group">
                    <p class="radio-group-label">23. Type of Damage <span class="asterisk">*</span></p>
                    <div class="radio-group">
                        <div><input type="radio" id="damage_discontinuity" name="type_of_damage" value="Discontinuity" required><label for="damage_discontinuity">Discontinuity</label></div>
                        <div><input type="radio" id="damage_na" name="type_of_damage" value="N/A"><label for="damage_na">N/A</label></div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="location_xy">24. Location (Ref: X/Y) <span class="asterisk">*</span></label>
                    <input type="text" id="location_xy" name="location_xy" required>
                </div>
                <div class="form-group">
                    <label for="amplitude_percent">25. Amplitude (%) <span class="asterisk">*</span></label>
                    <input type="text" id="amplitude_percent" name="amplitude_percent" required>
                </div>
                <div class="form-group">
                    <label for="length_mm">26. Length (mm) <span class="asterisk">*</span></label>
                    <input type="text" id="length_mm" name="length_mm" required>
                </div>
                <div class="form-group">
                    <label for="width_mm">27. Width (mm) <span class="asterisk">*</span></label>
                    <input type="text" id="width_mm" name="width_mm" required>
                </div>
                <div class="form-group">
                    <label for="depth_mm">28. Depth (mm) <span class="asterisk">*</span></label>
                    <input type="text" id="depth_mm" name="depth_mm" required>
                </div>
            </fieldset>

            <fieldset>
                <legend>Results of remain thickness inspection</legend>
                <div class="form-group">
                    <label for="thickness_result">29. Thickness Result <span class="asterisk">*</span></label>
                    <input type="text" id="thickness_result" name="thickness_result" required>
                </div>
                <div class="form-group">
                    <label for="min_thickness_value">30. Minimum thickness value <span class="asterisk">*</span></label>
                    <input type="text" id="min_thickness_value" name="min_thickness_value" required>
                </div>
                <div class="form-group">
                    <label for="thickness_location">31. Thickness Location <span class="asterisk">*</span></label>
                    <input type="text" id="thickness_location" name="thickness_location" required>
                </div>
            </fieldset>

            <fieldset>
                <legend>Final Status</legend>
                <div class="form-group">
                    <p class="radio-group-label">32. Final Status UT <span class="asterisk">*</span></p>
                    <div class="radio-group">
                        <div><input type="radio" id="final_status_pass" name="final_status_ut" value="Pass" required><label for="final_status_pass">Pass</label></div>
                        <div><input type="radio" id="final_status_no_pass" name="final_status_ut" value="No Pass"><label for="final_status_no_pass">No Pass</label></div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="acceptance_criteria">33. Acceptance Criteria <span class="asterisk">*</span></label>
                    <input type="text" id="acceptance_criteria" name="acceptance_criteria" required>
                </div>
            </fieldset>

            <fieldset>
                <legend>Inspector and Additional Data</legend>
                <div class="form-group">
                    <label for="inspector_perform_inspection">34. Inspector who perform Inspection <span class="asterisk">*</span></label>
                    <select id="inspector_perform_inspection" name="inspector_perform_inspection" required>
                        <option value="" disabled selected>Seleccione un inspector</option>
                        <option value="Reynol Aguilar">Reynol Aguilar</option>
                        <option value="Francisco Ayala">Francisco Ayala</option>
                        <option value="Juan Briones">Juan Briones</option>
                        <option value="Juan Cabello">Juan Cabello</option>
                        <option value="Victor Carranza">Victor Carranza</option>
                        <option value="Daniel Cerino">Daniel Cerino</option>
                        <option value="Roberto Diaz">Roberto Diaz</option>
                        <option value="Leonardo Galvan">Leonardo Galvan</option>
                        <option value="Abel Garrido">Abel Garrido</option>
                        <option value="Mariano Iracheta">Mariano Iracheta</option>
                        <option value="Margarita Otero">Margarita Otero</option>
                        <option value="Edgar Perez">Edgar Perez</option>
                        <option value="Carlos Ramirez">Carlos Ramirez</option>
                        <option value="Raul Ramirez">Raul Ramirez</option>
                        <option value="Armando Rodarte">Armando Rodarte</option>
                        <option value="Victor Rubio">Victor Rubio</option>
                        <option value="Daniel Sala">Daniel Sala</option>
                        <option value="Lorena Ugalde">Lorena Ugalde</option>
                        <option value="Kenton Rosales">Kenton Rosales</option>
                        <option value="Luis Zabala">Luis Zabala</option>
                        <option value="Noe Campero">Noe Campero</option>
                        <option value="Aldo Torres">Aldo Torres</option> <!-- Aldo Torres aparece dos veces, se incluye una -->
                        <option value="Erick Ruiz">Erick Ruiz</option>
                        <option value="Alfredo Infante">Alfredo Infante</option>
                        <option value="Rafael Velazco">Rafael Velazco</option>
                        <option value="Fabian Cavazos">Fabian Cavazos</option>
                        <option value="Hector Marin">Hector Marin</option>
                        <option value="Oscar Sanchez Zarate">Oscar Sanchez Zarate</option>
                        <option value="Ricardo Castañeda">Ricardo Castañeda</option>
                        <option value="Emedel de Los Santos">Emedel de Los Santos</option>
                        <option value="Jose Luis Jaime Quilantan">Jose Luis Jaime Quilantan</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="inspector_email">35. Put Your e-mail <span class="asterisk">*</span></label>
                    <select id="inspector_email" name="inspector_email" required>
                        <option value="" disabled selected>Seleccione su e-mail</option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option> <!-- Victor Carranza email aparece dos veces -->
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option> <!-- Jose Jaime email aparece dos veces -->
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                    </select>
                </div>

                <div class="form-group">
                    <p class="radio-group-label">36. Qualification Level <span class="asterisk">*</span></p>
                    <div class="radio-group">
                        <div><input type="radio" id="qual_trainee" name="qualification_level" value="Trainee" required><label for="qual_trainee">Trainee</label></div>
                        <div><input type="radio" id="qual_level1" name="qualification_level" value="Level 1"><label for="qual_level1">Level 1</label></div>
                        <div><input type="radio" id="qual_level2" name="qualification_level" value="Level 2"><label for="qual_level2">Level 2</label></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="ut_level2_approval">37. UT Level 2 Approval <span class="asterisk">*</span></label>
                    <select id="ut_level2_approval" name="ut_level2_approval" required>
                        <option value="" disabled selected>Seleccione aprobación Nivel 2</option>
                        <option value="Abel Garrido (NDT-006)">Abel Garrido (NDT-006)</option>
                        <option value="Carlos Ramirez (NDT-011)">Carlos Ramirez (NDT-011)</option>
                        <option value="Raul Ramirez (NDT-003)">Raul Ramirez (NDT-003)</option>
                        <option value="Armando Rodarte (NDT-013)">Armando Rodarte (NDT-013)</option>
                        <option value="Daniel Sala (NDT-001)">Daniel Sala (NDT-001)</option>
                        <option value="Ricardo Sifuentes (NDT-010)">Ricardo Sifuentes (NDT-010)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="ut_level2_stamp">38. UT Level 2 Stamp <span class="asterisk">*</span></label>
                    <select id="ut_level2_stamp" name="ut_level2_stamp" required>
                        <option value="" disabled selected>Seleccione sello Nivel 2</option>
                        <option value="NDT-001">NDT-001</option>
                        <option value="NDT-003">NDT-003</option>
                        <option value="NDT-006">NDT-006</option>
                        <option value="NDT-010">NDT-010</option>
                        <option value="NDT-011">NDT-011</option>
                        <option value="NDT-013">NDT-013</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="ut_level2_email">39. UT Level 2 E-mail <span class="asterisk">*</span></label>
                    <select id="ut_level2_email" name="ut_level2_email" required>
                        <option value="" disabled selected>Seleccione e-mail Nivel 2</option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                        <option value="<EMAIL>"><EMAIL></option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="ndt_time_elapsed">40. NDT Time Elapsed (Hours) <span class="asterisk">*</span></label>
                    <input type="number" id="ndt_time_elapsed" name="ndt_time_elapsed" min="0" step="0.1" required placeholder="Ej: 2.5">
                </div>

                <div class="form-group">
                    <label for="reference_used_final">41. Reference Used <span class="asterisk">*</span></label>
                    <input type="text" id="reference_used_final" name="reference_used_final" required>
                </div>
            </fieldset>

            <button type="submit">Submit Report</button>
        </form>

        <div class="footer-text">
            Este contenido no está creado ni respaldado por Microsoft. Los datos que envíe se enviarán al propietario del formulario.
            <div class="microsoft-forms-logo">
                <!-- <img src="path/to/ms-forms-logo.svg" alt="Microsoft Forms"> -->
                <span>Microsoft Forms Inspired</span>
            </div>
        </div>
    </div>

</body>
</html>