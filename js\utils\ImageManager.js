/**
 * ====================================================
 * IMAGE MANAGER
 * Gestión centralizada de imágenes para formularios BSI PW1100
 * Maneja subida a S3, localStorage y recuperación desde BD
 * ====================================================
 */

class ImageManager {
    constructor() {
        this.maxFileSize = 10 * 1024 * 1024; // 10MB
        this.allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        this.uploadedImages = new Map(); // Cache de imágenes subidas
        
        /* ================= AJUSTE DE ENTORNO AWS =================
           En navegador no existe require(); usamos window.AWS.
           Si el SDK no está presente simplemente deshabilitamos
           la subida a S3 y continuamos con la API local.   */

        try {
            if (typeof require === 'function') {
                // Ambiente Node / Electron
                this.AWS = require('aws-sdk');
            }
        } catch (e) {
            // ignore
        }

        if (!this.AWS && typeof window !== 'undefined' && window.AWS) {
            this.AWS = window.AWS;
        }

        if (this.AWS) {
            this.AWS.config.update({ region: 'us-east-2' });
            this.s3 = new this.AWS.S3();
            this.bucketName = 'viva-inspectores-storage';
            this.s3BaseUrl = `https://${this.bucketName}.s3.amazonaws.com/`;
            console.log('✅ AWS SDK detectado para ImageManager. Subida directa a S3 habilitada');
        } else {
            console.warn('⚠️ AWS SDK no encontrado para ImageManager. Se usa API local');
            this.s3 = null;
            this.bucketName = 'viva-inspectores-storage';
            this.s3BaseUrl = `https://${this.bucketName}.s3.amazonaws.com/`;
        }

        // No llamar init() aquí, se llamará desde initializeImageManager()
    }

    async init() {
        console.log('🖼️ Iniciando ImageManager...');
        console.log(`📏 Tamaño máximo: ${this.maxFileSize / (1024 * 1024)}MB`);
        console.log(`🎨 Tipos permitidos: ${this.allowedTypes.join(', ')}`);
        console.log(`🪣 Bucket S3: ${this.bucketName}`);
        console.log(`🔗 URL base S3: ${this.s3BaseUrl}`);
        console.log(`⚡ Subida directa S3: ${this.s3 ? 'HABILITADA' : 'DESHABILITADA'}`);

        // Esperar a que LocalStorageManager esté disponible
        await this.waitForLocalStorageManager();

        // Probar conexión S3 si está disponible
        if (this.s3) {
            this.testS3Connection().then(connected => {
                if (connected) {
                    console.log('🎯 ImageManager listo con S3 configurado');
                } else {
                    console.warn('⚠️ ImageManager iniciado pero S3 no disponible');
                }
            }).catch(error => {
                console.warn('⚠️ Error probando S3:', error.message);
            });
        }
    }

    /**
     * Esperar a que LocalStorageManager esté disponible
     */
    async waitForLocalStorageManager() {
        let attempts = 0;
        const maxAttempts = 50; // 5 segundos máximo

        while (!window.localStorageManager && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;

            if (attempts % 10 === 0) {
                console.log(`⏳ Esperando LocalStorageManager... ${attempts}/${maxAttempts}`);
            }
        }

        if (window.localStorageManager) {
            console.log('✅ LocalStorageManager detectado por ImageManager');

            // Verificar métodos críticos
            const criticalMethods = ['loadImages', 'saveImages'];
            for (const method of criticalMethods) {
                if (typeof window.localStorageManager[method] !== 'function') {
                    console.error(`❌ Método crítico ${method} no disponible en LocalStorageManager`);
                }
            }
        } else {
            console.error('❌ LocalStorageManager no disponible después de esperar');
        }
    }

    // ====================================================
    // MÉTODOS PRINCIPALES
    // ====================================================

    /**
     * Procesar archivos de imagen seleccionados
     * @param {FileList} files - Archivos seleccionados
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario (para modo edit)
     * @param {string} section - Sección del motor (ej: 'lpc_stage1', 'hpc_stage2', etc.)
     * @returns {Promise<Array>} - Array de imágenes procesadas
     */
    async processFiles(files, mode = 'draft', formId = null, section = 'general') {
        console.log(`🔄 Procesando ${files.length} archivos [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}] para sección: ${section}...`);
        
        const processedImages = [];
        const errors = [];
        
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            
            try {
                // Validar archivo
                const validation = this.validateFile(file);
                if (!validation.valid) {
                    errors.push(`${file.name}: ${validation.error}`);
                    continue;
                }
                
                // Crear objeto de imagen base
                const imageData = {
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    description: '',
                    originalFile: file,
                    uploadedAt: new Date().toISOString(),
                    id: this.generateImageId(),
                    section: section
                };
                
                // Generar preview para mostrar inmediatamente
                imageData.src = await this.generatePreview(file);
                
                // Subir a S3 inmediatamente con manejo de errores mejorado
                try {
                    const s3Result = await this.uploadToS3(file, mode, formId);
                    imageData.s3_key = s3Result.key;
                    imageData.s3_url = s3Result.url;
                    imageData.uploaded = true;
                    imageData.fallback = s3Result.fallback || false;

                    if (s3Result.fallback) {
                        console.log(`✅ Imagen guardada localmente (fallback): ${file.name}`);
                        imageData.localId = s3Result.localId;
                    } else {
                        console.log(`✅ Imagen subida a S3: ${file.name}`);
                    }

                } catch (s3Error) {
                    console.warn(`⚠️ Error subiendo ${file.name} a S3:`, s3Error);

                    // Intentar fallback manual si no se manejó automáticamente
                    try {
                        if (window.awsErrorHandler) {
                            const fallbackResult = await window.awsErrorHandler.handleAWSError(
                                s3Error,
                                'upload',
                                {
                                    file,
                                    key: `images/${mode}/${Date.now()}_${file.name}`,
                                    bucket: 'viva-inspectores-storage'
                                }
                            );

                            imageData.s3_key = fallbackResult.Key;
                            imageData.s3_url = fallbackResult.Location;
                            imageData.uploaded = true;
                            imageData.fallback = true;
                            imageData.localId = fallbackResult.localId;

                            console.log(`✅ Imagen guardada con fallback: ${file.name}`);
                        } else {
                            throw s3Error;
                        }
                    } catch (fallbackError) {
                        console.error(`❌ Fallback también falló para ${file.name}:`, fallbackError);
                        imageData.uploaded = false;
                        imageData.s3_error = s3Error.message;
                        imageData.fallback_error = fallbackError.message;
                    }
                }
                
                processedImages.push(imageData);
                
            } catch (error) {
                console.error(`❌ Error procesando ${file.name}:`, error);
                errors.push(`${file.name}: ${error.message}`);
            }
        }
        
        // Mostrar errores si los hay
        if (errors.length > 0) {
            this.showErrors(errors);
        }
        
        // Guardar en localStorage
        if (processedImages.length > 0) {
            await this.saveToStorage(processedImages, mode, formId, section);
        }
        
        console.log(`✅ Procesamiento completado: ${processedImages.length} imágenes válidas`);
        return processedImages;
    }

    /**
     * Subir imagen a S3
     * @param {File} file - Archivo de imagen
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     * @returns {Promise<Object>} - Resultado de la subida
     */
    async uploadToS3(file, mode = 'draft', formId = null) {
        // Si tenemos AWS SDK configurado, usar subida directa
        if (this.s3) {
            return await this.uploadToS3Direct(file, mode, formId);
        }
        
        // Fallback a API local
        const formData = new FormData();
        formData.append('image', file);
        formData.append('mode', mode);
        if (formId) formData.append('formId', formId);
        
        const response = await fetch('/api/upload-image-s3', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Error subiendo a S3');
        }
        
        return await response.json();
    }

    /**
     * Subir imagen directamente a S3 usando AWS SDK
     * @param {File} file - Archivo de imagen
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     * @returns {Promise<Object>} - Resultado de la subida
     */
    async uploadToS3Direct(file, mode = 'draft', formId = null) {
        if (!this.s3) {
            throw new Error('AWS SDK no está configurado');
        }

        console.log(`📤 Subiendo imagen directamente a S3: ${file.name}`);
        
        try {
            // Generar key única para la imagen - todas van a draft/
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const fileExtension = file.name.split('.').pop();
            const baseName = file.name.replace(/\.[^/.]+$/, "").replace(/[^a-zA-Z0-9]/g, '_');
            const key = `images/draft/${timestamp}_${baseName}.${fileExtension}`;
            
            // Convertir archivo a buffer
            const arrayBuffer = await file.arrayBuffer();
            const buffer = new Uint8Array(arrayBuffer);
            
            const uploadParams = {
                Bucket: this.bucketName,
                Key: key,
                Body: buffer,
                ContentType: file.type,
                Metadata: {
                    'original-name': file.name,
                    'upload-mode': mode,
                    'upload-date': new Date().toISOString()
                }
            };
            
            // Agregar form-id si está disponible
            if (formId) {
                uploadParams.Metadata['form-id'] = formId;
            }
            
            const result = await this.s3.upload(uploadParams).promise();
            
            console.log(`✅ Imagen subida exitosamente a S3: ${key}`);
            console.log(`📍 URL: ${result.Location}`);
            
            return {
                success: true,
                key: result.Key,
                url: result.Location,
                etag: result.ETag,
                bucket: result.Bucket,
                uploadedAt: new Date().toISOString(),
                size: file.size,
                type: file.type
            };
            
        } catch (error) {
            console.error('❌ Error subiendo imagen a S3:', error);
            throw new Error(`Error subiendo ${file.name} a S3: ${error.message}`);
        }
    }

    /**
     * Verificar si una imagen existe en S3
     * @param {string} key - Clave de la imagen en S3
     * @returns {Promise<boolean>} - True si existe
     */
    async checkImageExistsInS3(key) {
        if (!this.s3) {
            return false;
        }
        
        try {
            await this.s3.headObject({
                Bucket: this.bucketName,
                Key: key
            }).promise();
            
            return true;
            
        } catch (error) {
            if (error.statusCode === 404) {
                return false;
            }
            throw error;
        }
    }

    /**
     * Eliminar imagen de S3
     * @param {string} key - Clave de la imagen en S3
     * @returns {Promise<boolean>} - True si se eliminó exitosamente
     */
    async deleteImageFromS3(key) {
        if (!this.s3) {
            console.warn('⚠️ AWS SDK no disponible para eliminar imagen');
            return false;
        }
        
        try {
            await this.s3.deleteObject({
                Bucket: this.bucketName,
                Key: key
            }).promise();
            
            console.log(`✅ Imagen eliminada de S3: ${key}`);
            return true;
            
        } catch (error) {
            console.error(`❌ Error eliminando imagen de S3: ${key}`, error);
            return false;
        }
    }

    /**
     * Obtener URL pre-firmada para acceso temporal a imagen
     * @param {string} key - Clave de la imagen en S3
     * @param {number} expiresIn - Tiempo de expiración en segundos (default: 1 hora)
     * @returns {Promise<string>} - URL pre-firmada
     */
    async getSignedImageUrl(key, expiresIn = 3600) {
        if (!this.s3) {
            // Si no hay SDK, devolver URL pública
            return `${this.s3BaseUrl}${key}`;
        }
        
        try {
            const signedUrl = await this.s3.getSignedUrlPromise('getObject', {
                Bucket: this.bucketName,
                Key: key,
                Expires: expiresIn
            });
            
            return signedUrl;
            
        } catch (error) {
            console.error('❌ Error generando URL firmada:', error);
            // Fallback a URL pública
            return `${this.s3BaseUrl}${key}`;
        }
    }

    /**
     * Obtener URL firmada a través del endpoint del servidor
     * @param {string} key - Clave de la imagen en S3
     * @param {number} expiresIn - Tiempo de expiración en segundos (default: 1 hora)
     * @returns {Promise<string>} - URL pre-firmada
     */
    async getSignedUrlViaAPI(key, expiresIn = 3600) {
        try {
            const response = await fetch('/api/get-presigned-url', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    key: key,
                    expiresIn: expiresIn
                })
            });
            
            if (!response.ok) {
                throw new Error(`Error HTTP: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.error || 'Error obteniendo URL firmada');
            }
            
            return data.url;
            
        } catch (error) {
            console.error('❌ Error obteniendo URL firmada del servidor:', error);
            // Fallback a URL pública como último recurso
            return `${this.s3BaseUrl}${key}`;
        }
    }

    /**
     * Cargar imágenes desde localStorage
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     * @returns {Promise<Array>} - Array de imágenes
     */
    async loadFromStorage(mode = 'draft', formId = null) {
        console.log(`📥 Cargando imágenes desde localStorage [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}]...`);

        let images = [];

        // Verificar que LocalStorageManager esté disponible
        if (!window.localStorageManager) {
            console.warn('⚠️ LocalStorageManager no disponible para cargar imágenes');

            // Intentar cargar desde sessionStorage como fallback
            try {
                const key = `temp_images_${mode}_${formId || 'draft'}`;
                const tempData = sessionStorage.getItem(key);
                if (tempData) {
                    images = JSON.parse(tempData);
                    console.log(`📦 ${images.length} imágenes cargadas desde sessionStorage temporal`);
                }
            } catch (error) {
                console.warn('⚠️ Error cargando desde sessionStorage:', error);
            }

            return images;
        }

        // Verificar que el método loadImages esté disponible
        if (typeof window.localStorageManager.loadImages !== 'function') {
            console.error('❌ Método loadImages no disponible en LocalStorageManager');
            return [];
        }

        try {
            images = window.localStorageManager.loadImages(mode, formId);

            // Verificar disponibilidad de imágenes en S3
            for (const image of images) {
                if (image.s3_url && image._large_file) {
                    try {
                        // Para imágenes grandes, verificar si están disponibles en S3
                        const available = await this.checkS3Availability(image.s3_url);
                        image.s3_available = available;
                    } catch (error) {
                        console.warn(`⚠️ Error verificando disponibilidad de ${image.name}:`, error);
                        image.s3_available = false;
                    }
                }
            }

        } catch (error) {
            console.error('❌ Error cargando imágenes desde localStorage:', error);
            images = [];
        }

        return images;
    }

    /**
     * Cargar metadatos de imágenes desde datos del formulario enviado
     * NO carga las imágenes reales hasta que se necesiten para el PDF
     * @param {Array} imageData - Datos de imágenes desde BD
     * @returns {Promise<Array>} - Array de metadatos de imágenes
     */
    async loadFromSubmittedForm(imageData) {
        console.log(`📥 Cargando metadatos de ${imageData.length} imágenes desde formulario enviado...`);
        
        const images = [];
        
        for (const imgData of imageData) {
            try {
                const image = {
                    name: imgData.name,
                    description: imgData.description || '',
                    size: imgData.size,
                    type: imgData.type,
                    s3_key: imgData.s3_key,
                    s3_url: imgData.s3_url,
                    id: imgData.id || this.generateImageId(),
                    uploaded: true,
                    loaded: false, // No cargamos la imagen aún
                    src: this.createPlaceholderImage(imgData.name), // Mostrar placeholder
                    originalFile: null // Se cargará cuando se necesite
                };
                
                images.push(image);
                console.log(`📋 Metadatos cargados para: ${imgData.name}`);
                
            } catch (error) {
                console.error(`❌ Error procesando metadatos de imagen ${imgData.name}:`, error);
            }
        }
        
        console.log(`✅ ${images.length} metadatos de imágenes cargados. Imágenes reales se cargarán al generar PDF.`);
        return images;
    }

    /**
     * Cargar imágenes reales desde S3 para generar PDF
     * @param {Array} imageMetadata - Array de metadatos de imágenes
     * @returns {Promise<Array>} - Array de imágenes con datos reales cargados
     */
    async loadImagesForPDF(imageMetadata) {
        console.log(`📄 Cargando ${imageMetadata.length} imágenes reales para generación de PDF...`);
        
        const loadedImages = [];
        
        for (const imgMeta of imageMetadata) {
            try {
                const image = { ...imgMeta };
                
                // Solo cargar si no está ya cargada
                if (!image.loaded && (image.s3_key || image.localId)) {
                    try {
                        console.log(`📸 Cargando imagen para PDF: ${image.name}`);

                        let blob;

                        // Si es una imagen de fallback local, cargarla desde localStorage
                        if (image.fallback && image.localId && window.awsErrorHandler) {
                            console.log(`📦 Cargando desde almacenamiento local: ${image.name}`);
                            const dataUrl = await window.awsErrorHandler.handleSignedUrlFallback({
                                key: image.s3_key,
                                fileId: image.localId
                            });

                            // Convertir data URL a blob
                            const response = await fetch(dataUrl);
                            blob = await response.blob();

                        } else if (image.s3_key) {
                            // Cargar desde S3
                            console.log(`☁️ Cargando desde S3: ${image.name}`);

                            try {
                                // Obtener URL firmada para acceder a la imagen privada
                                const signedUrl = await this.getSignedUrlViaAPI(image.s3_key);
                                blob = await this.loadImageFromS3(signedUrl);
                            } catch (s3Error) {
                                // Si falla S3, intentar fallback
                                if (window.awsErrorHandler) {
                                    console.log(`🔄 S3 falló, intentando fallback para: ${image.name}`);
                                    const dataUrl = await window.awsErrorHandler.handleSignedUrlFallback({
                                        key: image.s3_key
                                    });
                                    const response = await fetch(dataUrl);
                                    blob = await response.blob();
                                } else {
                                    throw s3Error;
                                }
                            }
                        }

                        if (blob) {
                            image.src = URL.createObjectURL(blob);
                            image.originalFile = new File([blob], image.name, { type: image.type });
                            image.loaded = true;

                            console.log(`✅ Imagen cargada para PDF: ${image.name}`);
                        } else {
                            throw new Error('No se pudo obtener blob de la imagen');
                        }

                    } catch (error) {
                        console.warn(`⚠️ Error cargando ${image.name} para PDF:`, error);
                        image.loaded = false;
                        image.error = error.message;

                        // Mantener placeholder para imagen no disponible
                        image.src = this.createPlaceholderImage(image.name);
                    }
                } else if (image.loaded) {
                    console.log(`✅ Imagen ya cargada: ${image.name}`);
                }
                
                loadedImages.push(image);
                
            } catch (error) {
                console.error(`❌ Error procesando imagen ${imgMeta.name} para PDF:`, error);
                loadedImages.push(imgMeta); // Agregar con placeholder
            }
        }
        
        console.log(`📄 ${loadedImages.length} imágenes preparadas para PDF`);
        return loadedImages;
    }

    /**
     * Guardar imágenes en localStorage
     * @param {Array} images - Array de imágenes
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     */
    async saveToStorage(images, mode = 'draft', formId = null, section = 'general') {
        console.log(`💾 Guardando ${images.length} imágenes en localStorage [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}] para sección: ${section}...`);

        // Verificar que LocalStorageManager esté disponible
        if (!window.localStorageManager) {
            console.error('❌ LocalStorageManager no disponible, intentando inicializar...');

            // Intentar esperar a que se inicialice
            let attempts = 0;
            while (!window.localStorageManager && attempts < 10) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
                console.log(`⏳ Esperando LocalStorageManager... intento ${attempts}/10`);
            }

            if (!window.localStorageManager) {
                console.error('❌ LocalStorageManager no disponible después de esperar');

                // Fallback: guardar en sessionStorage temporalmente
                try {
                    const key = `temp_images_${mode}_${formId || 'draft'}_${section}`;
                    sessionStorage.setItem(key, JSON.stringify(images));
                    console.log('💾 Imágenes guardadas temporalmente en sessionStorage');
                    return;
                } catch (error) {
                    console.error('❌ Error guardando en sessionStorage:', error);
                    throw new Error('No se pudo guardar las imágenes: LocalStorageManager no disponible');
                }
            }
        }

        // Verificar que el método saveImages esté disponible
        if (typeof window.localStorageManager.saveImages !== 'function') {
            console.error('❌ Método saveImages no disponible en LocalStorageManager');
            throw new Error('LocalStorageManager.saveImages no es una función');
        }

        try {
            window.localStorageManager.saveImages(images, mode, formId, section);
            console.log('✅ Imágenes guardadas exitosamente en localStorage');
        } catch (error) {
            console.error('❌ Error guardando en localStorage:', error);
            throw error;
        }
    }

    /**
     * Limpiar imágenes del localStorage
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     */
    clearStorage(mode = 'draft', formId = null) {
        console.log(`🗑️ Limpiando imágenes de localStorage [${mode}${formId ? `:${formId.substring(0, 8)}` : ''}]...`);
        
        window.localStorageManager.clearImages(mode, formId);
    }

    // ====================================================
    // MÉTODOS UTILITARIOS
    // ====================================================

    /**
     * Validar archivo de imagen
     * @param {File} file - Archivo a validar
     * @returns {Object} - Resultado de validación
     */
    validateFile(file) {
        if (!file) {
            return { valid: false, error: 'Archivo no válido' };
        }
        
        if (!this.allowedTypes.includes(file.type)) {
            return { 
                valid: false, 
                error: `Tipo no permitido. Use: ${this.allowedTypes.join(', ')}` 
            };
        }
        
        if (file.size > this.maxFileSize) {
            const maxSizeMB = this.maxFileSize / (1024 * 1024);
            return { 
                valid: false, 
                error: `Archivo muy grande. Máximo: ${maxSizeMB}MB` 
            };
        }
        
        return { valid: true };
    }

    /**
     * Generar preview de imagen
     * @param {File} file - Archivo de imagen
     * @returns {Promise<string>} - Data URL de la imagen
     */
    generatePreview(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                resolve(e.target.result);
            };
            
            reader.onerror = (error) => {
                reject(error);
            };
            
            reader.readAsDataURL(file);
        });
    }

    /**
     * Verificar disponibilidad de imagen en S3
     * @param {string} s3Url - URL de S3
     * @returns {Promise<boolean>} - True si está disponible
     */
    async checkS3Availability(s3Url) {
        // Si tenemos AWS SDK, extraer key de la URL y usar método directo
        if (this.s3 && s3Url.includes(this.bucketName)) {
            try {
                const key = s3Url.split(`${this.bucketName}/`)[1];
                if (key) {
                    return await this.checkImageExistsInS3(key);
                }
            } catch (error) {
                console.warn('⚠️ Error verificando con AWS SDK, usando método HTTP:', error);
            }
        }
        
        // Método HTTP tradicional como fallback
        try {
            const response = await fetch(s3Url, { method: 'HEAD' });
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    /**
     * Cargar imagen desde S3
     * @param {string} s3Url - URL de S3
     * @returns {Promise<Blob>} - Blob de la imagen
     */
    async loadImageFromS3(s3Url) {
        const response = await fetch(s3Url);
        
        if (!response.ok) {
            throw new Error(`Error cargando imagen: ${response.status}`);
        }
        
        return await response.blob();
    }

    /**
     * Crear imagen placeholder
     * @param {string} imageName - Nombre de la imagen
     * @returns {string} - Data URL del placeholder
     */
    createPlaceholderImage(imageName) {
        const canvas = document.createElement('canvas');
        canvas.width = 200;
        canvas.height = 150;
        
        const ctx = canvas.getContext('2d');
        
        // Fondo gris claro
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Borde
        ctx.strokeStyle = '#dee2e6';
        ctx.lineWidth = 2;
        ctx.strokeRect(0, 0, canvas.width, canvas.height);
        
        // Icono de imagen
        ctx.fillStyle = '#6c757d';
        ctx.font = '24px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('📸', canvas.width / 2, canvas.height / 2 - 20);
        
        // Texto
        ctx.fillStyle = '#6c757d';
        ctx.font = '12px Arial';
        ctx.fillText('Imagen guardada en S3', canvas.width / 2, canvas.height / 2 + 5);
        
        // Nombre de archivo (truncado si es muy largo)
        const displayName = imageName.length > 20 ? imageName.substring(0, 20) + '...' : imageName;
        ctx.font = '10px Arial';
        ctx.fillText(displayName, canvas.width / 2, canvas.height / 2 + 20);
        
        ctx.fillText('Se cargará al generar PDF', canvas.width / 2, canvas.height / 2 + 35);
        
        return canvas.toDataURL();
    }

    /**
     * Generar ID único para imagen
     * @returns {string} - ID único
     */
    generateImageId() {
        return 'img_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Mostrar errores al usuario
     * @param {Array} errors - Array de errores
     */
    showErrors(errors) {
        const errorMessage = `Se encontraron errores:\n${errors.join('\n')}`;
        
        // Crear notificación de error
        const notification = document.createElement('div');
        notification.className = 'image-error-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 9999;
            max-width: 400px;
            font-size: 14px;
            line-height: 1.4;
        `;
        
        notification.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 8px;">
                <i class="fas fa-exclamation-triangle"></i> Errores de imágenes
            </div>
            <div style="font-size: 12px;">
                ${errors.map(error => `• ${error}`).join('<br>')}
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Remover después de 8 segundos
        setTimeout(() => {
            notification.remove();
        }, 8000);
    }

    /**
     * Crear notificación de éxito
     * @param {string} message - Mensaje de éxito
     */
    showSuccess(message) {
        const notification = document.createElement('div');
        notification.className = 'image-success-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 9999;
            max-width: 400px;
            font-size: 14px;
        `;
        
        notification.innerHTML = `
            <div>
                <i class="fas fa-check-circle"></i> ${message}
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Remover después de 3 segundos
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    /**
     * Obtener estadísticas de imágenes
     * @returns {Object} - Estadísticas
     */
    getStats() {
        return {
            maxFileSize: this.maxFileSize,
            maxFileSizeMB: this.maxFileSize / (1024 * 1024),
            allowedTypes: this.allowedTypes,
            uploadedImages: this.uploadedImages.size,
            s3BaseUrl: this.s3BaseUrl,
            bucketName: this.bucketName,
            awsSDKAvailable: !!this.s3,
            directS3Upload: !!this.s3
        };
    }

    /**
     * Limpiar cache de imágenes
     */
    clearCache() {
        this.uploadedImages.clear();
        console.log('🧹 Cache de imágenes limpiado');
    }

    /**
     * Probar conexión a S3
     * @returns {Promise<boolean>} - True si la conexión es exitosa
     */
    async testS3Connection() {
        if (!this.s3) {
            console.warn('⚠️ AWS SDK no está configurado');
            return false;
        }
        
        try {
            // Intentar acceder al bucket
            await this.s3.headBucket({ Bucket: this.bucketName }).promise();
            console.log('✅ Conexión S3 exitosa');
            return true;
            
        } catch (error) {
            console.error('❌ Error de conexión S3:', error);
            return false;
        }
    }

    /**
     * Obtener información del bucket S3
     * @returns {Promise<Object>} - Información del bucket
     */
    async getBucketInfo() {
        if (!this.s3) {
            return { 
                available: false, 
                error: 'AWS SDK no configurado' 
            };
        }
        
        try {
            const bucketLocation = await this.s3.getBucketLocation({ 
                Bucket: this.bucketName 
            }).promise();
            
            return {
                available: true,
                bucketName: this.bucketName,
                region: bucketLocation.LocationConstraint || 'us-east-1',
                url: this.s3BaseUrl
            };
            
        } catch (error) {
            return {
                available: false,
                error: error.message
            };
        }
    }

    /**
     * Actualizar descripción de imagen
     * @param {string} imageId - ID de la imagen
     * @param {string} description - Nueva descripción
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     */
    updateImageDescription(imageId, description, mode = 'draft', formId = null) {
        console.log(`📝 Actualizando descripción para imagen ${imageId}:`, description.substring(0, 50) + '...');
        
        // Actualizar en localStorage
        const images = window.localStorageManager.loadImages(mode, formId);
        const imageIndex = images.findIndex(img => img.id === imageId);
        
        if (imageIndex !== -1) {
            images[imageIndex].description = description;
            window.localStorageManager.saveImages(images, mode, formId);
            console.log('✅ Descripción actualizada en localStorage');
        } else {
            console.warn('⚠️ No se encontró la imagen para actualizar descripción');
        }
    }

    /**
     * Obtener todas las imágenes con sus descripciones
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     * @returns {Array} - Array de imágenes con descripciones
     */
    getImagesWithDescriptions(mode = 'draft', formId = null) {
        const images = window.localStorageManager.loadImages(mode, formId);
        return images.map(img => ({
            id: img.id,
            name: img.name,
            src: img.src,
            description: img.description || '',
            s3_url: img.s3_url,
            uploaded: img.uploaded || false
        }));
    }

    /**
     * Validar que todas las imágenes tengan descripción
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     * @returns {Object} - Resultado de validación
     */
    validateImageDescriptions(mode = 'draft', formId = null) {
        const images = window.localStorageManager.loadImages(mode, formId);
        const imagesWithoutDescription = images.filter(img => !img.description || img.description.trim() === '');
        
        return {
            valid: imagesWithoutDescription.length === 0,
            missingDescriptions: imagesWithoutDescription.map(img => img.name),
            totalImages: images.length
        };
    }

    /**
     * Crear HTML para previsualización de imagen con descripción
     * @param {Object} imageData - Datos de la imagen
     * @param {number} index - Índice de la imagen
     * @returns {string} - HTML de la previsualización
     */
    createImagePreviewHTML(imageData, index) {
        const truncatedName = imageData.name.length > 20 
            ? imageData.name.substring(0, 17) + '...' 
            : imageData.name;

        return `
            <div class="image-preview-item" data-index="${index}" data-image-id="${imageData.id}">
                <div class="image-preview-thumbnail">
                    <img src="${imageData.src}" alt="Preview ${index + 1}" loading="lazy">
                    <div class="image-overlay">
                        <span class="image-filename" title="${imageData.name}">${truncatedName}</span>
                        <div class="image-actions">
                            <button type="button" onclick="window.imageManager.removeImageById('${imageData.id}')" class="btn-remove-image" title="Eliminar imagen">
                                🗑️
                            </button>
                        </div>
                    </div>
                </div>
                <div class="image-description-container">
                    <label for="image_description_${imageData.id}" class="image-description-label">
                        📝 Descripción de la imagen ${index + 1}:
                    </label>
                    <textarea 
                        id="image_description_${imageData.id}" 
                        name="image_description_${imageData.id}"
                        placeholder="Ej: ENGINE ESN V17259, LPC 1.5 NO DAMAGE FOUND"
                        maxlength="500"
                        onchange="window.imageManager.updateImageDescriptionFromInput('${imageData.id}', this.value)"
                        onkeyup="window.imageManager.updateCharCount('${imageData.id}', this.value)"
                    >${imageData.description || ''}</textarea>
                    <div class="image-description-footer">
                        <span class="char-count" id="char_count_${imageData.id}">
                            ${(imageData.description || '').length}/500 caracteres
                        </span>
                        <span class="image-description-help">
                            💡 Describa brevemente qué muestra esta imagen
                        </span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Actualizar descripción desde el input
     * @param {string} imageId - ID de la imagen
     * @param {string} description - Nueva descripción
     */
    updateImageDescriptionFromInput(imageId, description) {
        // Determinar modo y formId según URL
        const urlParams = new URLSearchParams(window.location.search);
        const mode = urlParams.get('edit') ? 'edit' : 'draft';
        const formId = urlParams.get('edit') || null;
        
        this.updateImageDescription(imageId, description, mode, formId);
    }

    /**
     * Actualizar contador de caracteres
     * @param {string} imageId - ID de la imagen
     * @param {string} text - Texto actual
     */
    updateCharCount(imageId, text) {
        const charCountElement = document.getElementById(`char_count_${imageId}`);
        if (charCountElement) {
            const count = text.length;
            charCountElement.textContent = `${count}/500 caracteres`;
            charCountElement.style.color = count > 450 ? '#dc3545' : '#666';
        }
    }

    /**
     * Eliminar imagen por ID
     * @param {string} imageId - ID de la imagen
     */
    async removeImageById(imageId) {
        console.log(`🗑️ Eliminando imagen con ID: ${imageId}`);
        
        // Determinar modo y formId según URL
        const urlParams = new URLSearchParams(window.location.search);
        const mode = urlParams.get('edit') ? 'edit' : 'draft';
        const formId = urlParams.get('edit') || null;
        
        // Obtener imágenes actuales
        const images = window.localStorageManager.loadImages(mode, formId);
        const imageToRemove = images.find(img => img.id === imageId);
        
        // Si la imagen tiene s3_key, intentar eliminarla de S3
        if (imageToRemove && imageToRemove.s3_key) {
            try {
                await this.deleteImageFromS3(imageToRemove.s3_key);
                console.log('✅ Imagen eliminada de S3');
            } catch (error) {
                console.warn('⚠️ Error eliminando de S3 (continuando):', error);
            }
        }
        
        // Filtrar imágenes
        const filteredImages = images.filter(img => img.id !== imageId);
        
        // Guardar imágenes filtradas
        window.localStorageManager.saveImages(filteredImages, mode, formId);
        
        // Actualizar interfaz
        if (window.bsiHandler) {
            window.bsiHandler.refreshImagePreview();
        }
        
        console.log('✅ Imagen eliminada');
    }

    /**
     * Limpiar imágenes huérfanas de S3 (imágenes que no están en localStorage)
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     */
    async cleanupOrphanedImages(mode = 'draft', formId = null) {
        if (!this.s3) {
            console.warn('⚠️ AWS SDK no disponible para limpieza');
            return;
        }
        
        console.log(`🧹 Limpiando imágenes huérfanas en modo ${mode}${formId ? `:${formId}` : ''}...`);
        
        try {
            // Obtener imágenes actuales de localStorage
            const currentImages = window.localStorageManager.loadImages(mode, formId);
            const currentS3Keys = currentImages
                .filter(img => img.s3_key)
                .map(img => img.s3_key);
            
            // Listar imágenes en S3 para este modo
            const prefix = `images/${mode}/`;
            const params = {
                Bucket: this.bucketName,
                Prefix: prefix
            };
            
            const s3Objects = await this.s3.listObjectsV2(params).promise();
            
            if (s3Objects.Contents) {
                let cleanedCount = 0;
                
                for (const object of s3Objects.Contents) {
                    // Si la imagen de S3 no está en localStorage, es huérfana
                    if (!currentS3Keys.includes(object.Key)) {
                        try {
                            await this.deleteImageFromS3(object.Key);
                            cleanedCount++;
                            console.log(`🗑️ Imagen huérfana eliminada: ${object.Key}`);
                        } catch (error) {
                            console.warn(`⚠️ Error eliminando ${object.Key}:`, error);
                        }
                    }
                }
                
                console.log(`✅ Limpieza completada: ${cleanedCount} imágenes huérfanas eliminadas`);
            }
            
        } catch (error) {
            console.error('❌ Error en limpieza de imágenes huérfanas:', error);
        }
    }

    /**
     * Diagnóstico de problemas con imágenes
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     */
    async diagnoseImageProblems(mode = 'draft', formId = null) {
        console.group('🔍 DIAGNÓSTICO DE IMÁGENES');
        
        try {
            // Verificar localStorage
            const images = window.localStorageManager.loadImages(mode, formId);
            console.log(`📊 Imágenes en localStorage: ${images.length}`);
            
            if (images.length === 0) {
                console.log('⚠️ No hay imágenes en localStorage');
                console.groupEnd();
                return;
            }
            
            // Verificar cada imagen
            for (const image of images) {
                console.group(`📸 Imagen: ${image.name}`);
                console.log(`🆔 ID: ${image.id}`);
                console.log(`🔑 S3 Key: ${image.s3_key || 'NO DISPONIBLE'}`);
                console.log(`🔗 S3 URL: ${image.s3_url || 'NO DISPONIBLE'}`);
                console.log(`📤 Subida: ${image.uploaded ? 'SÍ' : 'NO'}`);
                console.log(`📦 Tamaño: ${image.size ? `${(image.size / 1024).toFixed(2)} KB` : 'NO DISPONIBLE'}`);
                
                // Verificar disponibilidad en S3
                if (image.s3_key) {
                    try {
                        const signedUrl = await this.getSignedUrlViaAPI(image.s3_key);
                        console.log(`🔗 URL firmada obtenida: ${signedUrl.substring(0, 50)}...`);
                        
                        const response = await fetch(signedUrl, { method: 'HEAD' });
                        console.log(`🌐 Acceso HTTP: ${response.ok ? 'OK' : 'ERROR'} (${response.status})`);
                    } catch (error) {
                        console.error(`❌ Error verificando acceso: ${error.message}`);
                    }
                }
                
                console.groupEnd();
            }
            
        } catch (error) {
            console.error('❌ Error en diagnóstico:', error);
        }
        
        console.groupEnd();
    }

    // ====================================================
    // MÉTODOS PARA MANEJO POR SECCIONES
    // ====================================================

    /**
     * Obtener imágenes de una sección específica
     * @param {string} section - Sección del motor
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     * @returns {Array} - Array de imágenes de la sección
     */
    getImagesBySection(section, mode = 'draft', formId = null) {
        const allImages = window.localStorageManager.loadImages(mode, formId) || [];
        return allImages.filter(img => img.section === section);
    }

    /**
     * Obtener todas las imágenes organizadas por sección
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     * @returns {Object} - Objeto con imágenes organizadas por sección
     */
    getImagesBySections(mode = 'draft', formId = null) {
        const allImages = window.localStorageManager.loadImages(mode, formId) || [];
        const sections = {};

        // Definir orden de las secciones
        const sectionOrder = [
            'lpc_stage1', 'lpc_stage2', 'lpc_stage3',
            'bearing3_front', 'bearing3_rear',
            'hpc_stage1', 'hpc_stage2', 'hpc_stage3', 'hpc_stage4',
            'hpc_stage5', 'hpc_stage6', 'hpc_stage7', 'hpc_stage8',
            'igniter', 'fuelnozzle', 'cch_inner', 'cch_outer',
            'shiplap',
            'hpt_vane', 'hpt_s1', 'hpt_s2',
            'lpt_s1', 'lpt_s2', 'lpt_s3',
            'general'
        ];

        // Inicializar secciones
        sectionOrder.forEach(section => {
            sections[section] = [];
        });

        // Organizar imágenes por sección
        allImages.forEach(img => {
            const section = img.section || 'general';
            if (sections[section]) {
                sections[section].push(img);
            } else {
                sections['general'].push(img);
            }
        });

        return sections;
    }

    /**
     * Actualizar preview de una sección específica
     * @param {string} section - Sección del motor
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     */
    updateSectionPreview(section, mode = 'draft', formId = null) {
        const images = this.getImagesBySection(section, mode, formId);
        const previewContainer = document.getElementById(`${section}_preview`);
        
        if (!previewContainer) return;
        
        if (images.length === 0) {
            previewContainer.classList.remove('has-images');
            previewContainer.style.display = 'none';
            previewContainer.innerHTML = '';
            return;
        }
        
        previewContainer.classList.add('has-images');
        previewContainer.style.display = 'block';
        
        previewContainer.innerHTML = images.map(img => `
            <div class="section-image-item" data-image-id="${img.id}">
                <img src="${img.src}" alt="${img.name}">
                <div class="image-info">
                    <div class="image-name">${img.name}</div>
                    <div class="image-description-container">
                        <label for="desc_${img.id}">Descripción:</label>
                        <textarea
                            id="desc_${img.id}"
                            class="image-description"
                            placeholder="Agregar descripción de la imagen..."
                            onchange="window.imageManager.updateImageDescription('${img.id}', this.value, '${mode}', '${formId}')"
                        >${img.description || ''}</textarea>
                    </div>
                </div>
                <button class="remove-btn" onclick="window.imageManager.removeImageFromSection('${img.id}', '${section}', '${mode}', '${formId}')">
                    ✕ Eliminar
                </button>
            </div>
        `).join('');
        
        // Marcar form-group como que tiene imágenes
        const formGroup = previewContainer.closest('.form-group');
        if (formGroup) {
            formGroup.classList.add('has-section-images');
        }
    }

    /**
     * Eliminar imagen de una sección
     * @param {string} imageId - ID de la imagen
     * @param {string} section - Sección del motor
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     */
    removeImageFromSection(imageId, section, mode = 'draft', formId = null) {
        console.log(`🗑️ Eliminando imagen ${imageId} de sección ${section}...`);

        // Obtener todas las imágenes
        const allImages = window.localStorageManager.loadImages(mode, formId) || [];

        // Filtrar para remover la imagen específica
        const filteredImages = allImages.filter(img => img.id !== imageId);

        // Guardar imágenes actualizadas
        window.localStorageManager.saveImages(filteredImages, mode, formId);

        // Actualizar preview de la sección
        this.updateSectionPreview(section, mode, formId);

        console.log(`✅ Imagen eliminada de sección ${section}`);
    }

    /**
     * Actualizar descripción de una imagen
     * @param {string} imageId - ID de la imagen
     * @param {string} description - Nueva descripción
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     */
    updateImageDescription(imageId, description, mode = 'draft', formId = null) {
        console.log(`📝 Actualizando descripción de imagen ${imageId}...`);

        // Obtener todas las imágenes
        const allImages = window.localStorageManager.loadImages(mode, formId) || [];

        // Encontrar y actualizar la imagen específica
        const imageIndex = allImages.findIndex(img => img.id === imageId);
        if (imageIndex !== -1) {
            allImages[imageIndex].description = description;

            // Guardar imágenes actualizadas
            window.localStorageManager.saveImages(allImages, mode, formId);

            console.log(`✅ Descripción actualizada para imagen ${imageId}: "${description}"`);
            return true;
        } else {
            console.error(`❌ Imagen ${imageId} no encontrada`);
            return false;
        }
    }

    /**
     * Mostrar vista previa de todas las secciones con imágenes
     * @param {string} mode - 'draft' o 'edit'
     * @param {string} formId - ID del formulario
     */
    refreshAllSectionPreviews(mode = 'draft', formId = null) {
        const sections = this.getImagesBySections(mode, formId);
        
        Object.keys(sections).forEach(section => {
            this.updateSectionPreview(section, mode, formId);
        });
        
        console.log(`🔄 Previews actualizados para todas las secciones`);
    }
}

// Función para crear instancia global de forma asíncrona
async function initializeImageManager() {
    console.log('🔄 Inicializando ImageManager...');

    const imageManager = new ImageManager();
    await imageManager.init();

    // Hacer disponible globalmente
    window.imageManager = imageManager;

    console.log('✅ ImageManager inicializado y disponible globalmente');
    return imageManager;
}

// Inicializar cuando el DOM esté listo
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeImageManager);
} else {
    // DOM ya está listo
    initializeImageManager();
}

// Función global para diagnosticar problemas con imágenes
window.diagnoseImages = async function(mode = 'edit', formId = null) {
    console.log('🔍 Iniciando diagnóstico de imágenes...');
    
    // Obtener formId desde URL si no se proporciona
    if (!formId) {
        const urlParams = new URLSearchParams(window.location.search);
        formId = urlParams.get('edit');
    }
    
    if (!formId) {
        console.log('⚠️ No se encontró formId. Usando diagnóstico general...');
        await imageManager.diagnoseImageProblems('draft', null);
        return;
    }
    
    console.log(`🔍 Diagnosticando imágenes para formulario: ${formId}`);
    await imageManager.diagnoseImageProblems(mode, formId);
};

// Función global para cargar imágenes para PDF
window.loadImagesForPDF = async function(imageMetadata) {
    console.log('📄 Cargando imágenes para generación de PDF...');
    return await imageManager.loadImagesForPDF(imageMetadata);
};

console.log('✅ ImageManager inicializado');
console.log('💡 Funciones disponibles:');
console.log('   - diagnoseImages() - Diagnosticar problemas con imágenes');
console.log('   - loadImagesForPDF() - Cargar imágenes para generación de PDF');
console.log('📁 Estructura del bucket: images/draft/ (todas las imágenes)'); 