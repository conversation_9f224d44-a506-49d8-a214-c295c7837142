# Diagrama Entidad-Relación - Sistema VIVA_INSPECTORES

## Versión Completa: Todas las Entidades y Atributos

```mermaid
erDiagram
    INSPECTORS {
        uuid inspector_id PK
        varchar employee_number UK
        varchar first_name
        varchar last_name
        varchar email UK
        varchar license_number UK
        date hire_date
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    INSPECTOR_CERTIFICATIONS {
        uuid certification_id PK
        uuid inspector_id FK
        enum certification_type
        varchar certification_number
        date issued_date
        date expiry_date
        varchar issuing_authority
        boolean is_current
        timestamp created_at
        timestamp updated_at
    }
    
    AIRCRAFT {
        uuid aircraft_id PK
        varchar registration UK
        varchar manufacturer
        varchar model
        varchar serial_number UK
        year year_manufactured
        date entry_service_date
        boolean is_active_fleet
        timestamp created_at
        timestamp updated_at
    }
    
    ENGINES {
        uuid engine_id PK
        uuid aircraft_id FK
        enum engine_position
        varchar manufacturer
        varchar model
        varchar serial_number UK
        int total_cycles
        decimal total_flight_hours
        date installation_date
        date last_overhaul_date
        timestamp created_at
        timestamp updated_at
    }
    
    INSPECTIONS {
        uuid inspection_id PK
        varchar work_order_number
        uuid aircraft_id FK
        uuid inspector_id FK
        enum inspection_type
        uuid engine_id FK
        varchar station_code
        json form_data
        json validation_data
        enum status
        text findings_summary
        text recommendations
        datetime started_at
        datetime completed_at
        datetime approved_at
        uuid approved_by FK
        timestamp created_at
        timestamp updated_at
    }
    
    INSPECTION_EVIDENCE {
        uuid evidence_id PK
        uuid inspection_id FK
        varchar file_name
        text file_path
        bigint file_size_bytes
        varchar mime_type
        int image_width
        int image_height
        text description
        int sequence_order
        timestamp uploaded_at
        uuid uploaded_by FK
    }
    
    OFFICIAL_REPORTS {
        uuid report_id PK
        uuid inspection_id FK
        enum document_type
        text file_path
        bigint file_size_bytes
        varchar checksum_md5
        timestamp generated_at
        uuid generated_by FK
        varchar template_version
        boolean is_official_copy
    }
    
    AUDIT_LOG {
        uuid audit_id PK
        varchar table_name
        varchar record_id
        enum operation_type
        json old_values
        json new_values
        uuid changed_by FK
        timestamp changed_at
        varchar ip_address
        text user_agent
        varchar session_id
    }

    %% Relaciones simplificadas
    INSPECTORS ||--o{ INSPECTOR_CERTIFICATIONS : "tiene"
    INSPECTORS ||--o{ INSPECTIONS : "realiza"
    INSPECTORS ||--o{ INSPECTIONS : "aprueba"
    INSPECTORS ||--o{ INSPECTION_EVIDENCE : "sube"
    INSPECTORS ||--o{ OFFICIAL_REPORTS : "genera"
    INSPECTORS ||--o{ AUDIT_LOG : "modifica"
    
    AIRCRAFT ||--o{ ENGINES : "tiene"
    AIRCRAFT ||--o{ INSPECTIONS : "inspecciona"
    
    ENGINES ||--o{ INSPECTIONS : "inspecciona_motor"
    
    INSPECTIONS ||--o{ INSPECTION_EVIDENCE : "documenta"
    INSPECTIONS ||--|| OFFICIAL_REPORTS : "genera"
```

---

## Versión Simplificada: Solo Entidades Principales

```mermaid
erDiagram
    INSPECTORS {
        uuid inspector_id PK
        varchar employee_number
        varchar first_name
        varchar last_name
        varchar license_number
    }
    
    INSPECTOR_CERTIFICATIONS {
        uuid certification_id PK
        uuid inspector_id FK
        enum certification_type
        date expiry_date
    }
    
    AIRCRAFT {
        uuid aircraft_id PK
        varchar registration
        varchar manufacturer
        varchar model
    }
    
    ENGINES {
        uuid engine_id PK
        uuid aircraft_id FK
        enum engine_position
        varchar serial_number
    }
    
    INSPECTIONS {
        uuid inspection_id PK
        varchar work_order_number
        uuid aircraft_id FK
        uuid inspector_id FK
        enum inspection_type
        uuid engine_id FK
        enum status
        json form_data
    }
    
    INSPECTION_EVIDENCE {
        uuid evidence_id PK
        uuid inspection_id FK
        varchar file_name
        text description
    }
    
    OFFICIAL_REPORTS {
        uuid report_id PK
        uuid inspection_id FK
        enum document_type
        text file_path
    }

    %% Relaciones principales
    INSPECTORS ||--o{ INSPECTOR_CERTIFICATIONS : "certifica"
    INSPECTORS ||--o{ INSPECTIONS : "realiza"
    
    AIRCRAFT ||--o{ ENGINES : "contiene"
    AIRCRAFT ||--o{ INSPECTIONS : "inspecciona"
    
    ENGINES ||--o{ INSPECTIONS : "inspecciona_motor"
    
    INSPECTIONS ||--o{ INSPECTION_EVIDENCE : "evidencia"
    INSPECTIONS ||--|| OFFICIAL_REPORTS : "reporta"
```

---

## Descripción de Relaciones

### Cardinalidades Principales:
- **INSPECTORS (1) → (N) INSPECTIONS**: Un inspector puede realizar múltiples inspecciones
- **AIRCRAFT (1) → (N) ENGINES**: Una aeronave tiene múltiples motores 
- **AIRCRAFT (1) → (N) INSPECTIONS**: Una aeronave puede tener múltiples inspecciones
- **ENGINES (1) → (N) INSPECTIONS**: Un motor específico puede tener múltiples inspecciones
- **INSPECTIONS (1) → (N) INSPECTION_EVIDENCE**: Una inspección puede tener múltiple evidencia
- **INSPECTIONS (1) → (1) OFFICIAL_REPORTS**: Cada inspección genera un reporte oficial

### Relaciones de Integridad:
- **Inspector ↔ Certificación**: Control de autorización por tipo de inspección
- **Aeronave ↔ Motor**: Gestión de componentes específicos por aeronave
- **Aeronave ↔ Inspección**: Inspecciones directas a nivel de aeronave
- **Motor ↔ Inspección**: Inspecciones específicas por motor (BSI, etc.)
- **Inspección ↔ Evidencia**: Trazabilidad de documentación fotográfica

### Llaves y Restricciones:
- **PK**: Primary Key (Clave Primaria) - UUID para identificación única
- **FK**: Foreign Key (Clave Foránea) - Relaciones entre tablas
- **UK**: Unique Key (Clave Única) - Campos con restricción de unicidad

## Uso del Diagrama
- **Versión Completa**: Para desarrollo y documentación técnica detallada
- **Versión Simplificada**: Para presentaciones ejecutivas y análisis de alto nivel 