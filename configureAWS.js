/**
 * ====================================================
 * CONFIGURACIÓN DE AWS PARA VIVA INSPECTORES
 * Configuración mejorada con fallback a almacenamiento local
 * ====================================================
 */

// Configuración de AWS
const AWS_CONFIG = {
    // CAMBIAR A FALSE PARA HABILITAR AWS EN PRODUCCIÓN
    development: true, // 🔧 TEMPORAL: Cambiado a true para usar mock mientras se configura AWS real

    // Configuración de AWS (completar con credenciales reales)
    region: 'us-east-2',
    bucket: 'viva-inspectores-storage',

    // IMPORTANTE: En producción, estas credenciales deben venir de un servidor seguro
    // NO hardcodear credenciales reales aquí por seguridad
    accessKeyId: 'AKIAIOSFODNN7EXAMPLE', // ⚠️ CAMBIAR POR CREDENCIALES REALES
    secretAccessKey: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY', // ⚠️ CAMBIAR POR CREDENCIALES REALES

    // Configuración de fallback local
    enableLocalFallback: true, // Si AWS falla, usar almacenamiento local
    localStoragePrefix: 'viva_images_'
};

// Función para verificar si AWS está disponible
async function testAWSConnection() {
    if (!window.AWS || AWS_CONFIG.development) {
        return false;
    }

    try {
        const s3 = new window.AWS.S3();
        await s3.headBucket({ Bucket: AWS_CONFIG.bucket }).promise();
        return true;
    } catch (error) {
        console.warn('⚠️ AWS no disponible:', error.message);
        return false;
    }
}

// Configurar AWS según el modo
if (AWS_CONFIG.development) {
    console.log('🔧 Modo desarrollo: Usando almacenamiento local como principal');

    // Configurar mock de AWS que simula éxito para desarrollo
    window.AWS = {
        config: {
            update: function(config) {
                console.log('📝 AWS config (desarrollo) actualizado:', config);
                return Promise.resolve();
            }
        },
        S3: function() {
            return {
                upload: function(params) {
                    return {
                        promise: function() {
                            // Simular subida exitosa en desarrollo
                            const mockResult = {
                                Key: params.Key,
                                Location: `https://${AWS_CONFIG.bucket}.s3.amazonaws.com/${params.Key}`,
                                Bucket: params.Bucket,
                                ETag: '"' + Math.random().toString(36).substring(2) + '"'
                            };
                            console.log('🎭 Mock AWS upload exitoso:', params.Key);
                            return Promise.resolve(mockResult);
                        }
                    };
                },
                headBucket: function() {
                    return {
                        promise: function() {
                            return Promise.resolve({ LocationConstraint: AWS_CONFIG.region });
                        }
                    };
                },
                headObject: function() {
                    return {
                        promise: function() {
                            return Promise.resolve({ ContentLength: 1024 });
                        }
                    };
                },
                deleteObject: function() {
                    return {
                        promise: function() {
                            return Promise.resolve({});
                        }
                    };
                },
                getSignedUrlPromise: function(operation, params) {
                    // Mock URL firmada para desarrollo
                    const mockUrl = `https://${AWS_CONFIG.bucket}.s3.amazonaws.com/${params.Key}?mock=true&operation=${operation}`;
                    console.log(`🎭 Mock signed URL generada para ${operation}:`, params.Key);
                    return Promise.resolve(mockUrl);
                }
            };
        }
    };

    // Configurar AWS mock
    window.AWS.config.update({
        region: AWS_CONFIG.region,
        accessKeyId: 'MOCK_ACCESS_KEY',
        secretAccessKey: 'MOCK_SECRET_KEY'
    });

} else {
    // Configuración real de AWS para producción
    if (window.AWS) {
        try {
            window.AWS.config.update({
                accessKeyId: AWS_CONFIG.accessKeyId,
                secretAccessKey: AWS_CONFIG.secretAccessKey,
                region: AWS_CONFIG.region
            });

            console.log('✅ AWS configurado para producción');

            // Probar conexión
            testAWSConnection().then(connected => {
                if (connected) {
                    console.log('🎯 Conexión AWS verificada exitosamente');
                } else {
                    console.warn('⚠️ AWS configurado pero conexión falló. Usando fallback local.');
                }
            });

        } catch (error) {
            console.error('❌ Error configurando AWS:', error);
            console.log('🔄 Usando fallback a almacenamiento local');
        }
    } else {
        console.warn('⚠️ AWS SDK no encontrado. Usando almacenamiento local.');
    }
}

// Hacer configuración disponible globalmente
window.AWS_CONFIG = AWS_CONFIG;
window.testAWSConnection = testAWSConnection;

// Función de utilidad para obtener configuración actual
window.getAWSStatus = function() {
    return {
        development: AWS_CONFIG.development,
        region: AWS_CONFIG.region,
        bucket: AWS_CONFIG.bucket,
        sdkLoaded: !!window.AWS,
        fallbackEnabled: AWS_CONFIG.enableLocalFallback
    };
};

// Función para cambiar modo (útil para debugging)
window.toggleAWSMode = function(enableAWS = null) {
    if (enableAWS !== null) {
        AWS_CONFIG.development = !enableAWS;
    } else {
        AWS_CONFIG.development = !AWS_CONFIG.development;
    }

    console.log(`🔄 Modo AWS ${AWS_CONFIG.development ? 'DESHABILITADO' : 'HABILITADO'}`);
    console.log('🔄 Recarga la página para aplicar cambios');

    return AWS_CONFIG.development;
};

console.log('🔧 configureAWS.js cargado');
console.log(`📊 Estado AWS: ${AWS_CONFIG.development ? 'DESARROLLO (Mock)' : 'PRODUCCIÓN'}`);
console.log(`🪣 Bucket: ${AWS_CONFIG.bucket}`);
console.log(`🌍 Región: ${AWS_CONFIG.region}`);
console.log('💡 Funciones disponibles:');
console.log('   - getAWSStatus() - Ver estado actual');
console.log('   - toggleAWSMode() - Cambiar modo AWS');
console.log('   - testAWSConnection() - Probar conexión');