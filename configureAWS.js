/**
 * ====================================================
 * CONFIGURACIÓN DE AWS PARA DESARROLLO
 * Este archivo proporciona configuración mock para desarrollo
 * En producción usar credenciales reales de AWS
 * ====================================================
 */

// Configuración para desarrollo/testing - NO usar en producción
const AWS_CONFIG = {
    // Modo de desarrollo: desactiva AWS y usa almacenamiento local
    development: true,
    
    // Para producción, descomentar y completar:
    // accessKeyId: 'TU_ACCESS_KEY_ID',
    // secretAccessKey: 'TU_SECRET_ACCESS_KEY', 
    // region: 'us-east-2',
    // bucket: 'viva-inspectores-storage'
};

// Verificar si estamos en modo desarrollo
if (AWS_CONFIG.development) {
    console.log('🔧 Modo desarrollo: AWS deshabilitado, usando almacenamiento local');
    
    // Mock AWS para desarrollo
    window.AWS = {
        config: {
            update: function() {
                console.log('📝 AWS config (desarrollo) actualizado');
            }
        },
        S3: function() {
            return {
                upload: function() {
                    return {
                        promise: function() {
                            // Simular error de AWS para forzar fallback
                            return Promise.reject(new Error('AWS deshabilitado en desarrollo'));
                        }
                    };
                }
            };
        }
    };
} else {
    // Configuración real de AWS para producción
    if (window.AWS) {
        window.AWS.config.update({
            accessKeyId: AWS_CONFIG.accessKeyId,
            secretAccessKey: AWS_CONFIG.secretAccessKey,
            region: AWS_CONFIG.region
        });
        console.log('✅ AWS configurado para producción');
    }
}

console.log('🔧 configureAWS.js cargado');