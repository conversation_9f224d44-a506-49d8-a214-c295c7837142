# Instrucciones para usar DBML en dbdiagram.io

## 🚀 Pasos Rápidos:

### 1. Accede a dbdiagram.io
- Ve a https://dbdiagram.io/
- Crea cuenta gratuita o haz login

### 2. Importa el archivo DBML
- Click en "New Diagram"
- Selecciona "Import" en el menú
- Choose "From DBML"

### 3. Copia el código DBML
- Abre el archivo `viva_inspectores_dbml.dbml`
- Selecciona TODO el contenido (Ctrl+A)
- Copia (Ctrl+C)

### 4. Pega en dbdiagram.io
- Pega el código en el editor de la izquierda
- ¡El diagrama se genera automáticamente en el panel derecho!

### 5. Personaliza y exporta
- Ajusta la vista (zoom, posición de tablas)
- Export → PNG/SVG/PDF para imágenes
- Export → SQL para generar scripts DDL de MariaDB

## 🎨 Características Especiales del Diagrama:

### Colores por Tipo de Tabla:
- **🔵 Azul (#3498DB)**: Inspectores y certificaciones
- **🟢 Verde (#27AE60)**: Aeronaves  
- **🟠 Naranja (#F39C12)**: Motores
- **🟣 Morado (#9B59B6)**: Inspecciones
- **🔷 Turquesa (#1ABC9C)**: Evidencia fotográfica
- **🟤 Café (#E67E22)**: Reportes oficiales
- **⚫ Gris (#34495E)**: Auditoría

### Documentación Integrada:
- Cada campo tiene notas explicativas
- Enums con valores específicos documentados
- Relaciones con descripciones claras
- Índices optimizados para rendimiento

### Funcionalidades de dbdiagram.io:
- **Zoom interactivo** para navegar tablas grandes
- **Búsqueda de tablas** por nombre
- **Vista de relaciones** resaltando conexiones
- **Modo presentación** para stakeholders
- **Colaboración en tiempo real** (plan pro)
- **Versionado de esquemas** (plan pro)

## 📊 Exportación Recomendada:

### Para Documentación:
- **PNG (300 DPI)** - Para documentos Word/PowerPoint
- **SVG** - Para documentos técnicos escalables

### Para Desarrollo:
- **SQL (MariaDB)** - Scripts DDL listos para ejecutar
- **PDF** - Para revisiones impresas

### Para Presentaciones:
- **PNG de alta resolución** con fondo transparente
- Recorta las secciones específicas según audiencia

## 🔧 Modificaciones Futuras:

Si necesitas modificar el esquema:
1. Edita el archivo `viva_inspectores_dbml.dbml`
2. Copia el código actualizado
3. Pega en dbdiagram.io
4. El diagrama se actualiza automáticamente

**¡El archivo DBML es la fuente de verdad para tu esquema de base de datos!** 