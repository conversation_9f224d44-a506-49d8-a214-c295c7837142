# ====================================================
# CONFIGURACIÓN DE ENTORNO PARA VIVA INSPECTORES
# Copiar este archivo a .env y completar con valores reales
# ====================================================

# Base de datos
DB_HOST=localhost
DB_USER=tu_usuario
DB_PASSWORD=tu_contraseña
DB_NAME=viva_inspectores
DB_PORT=3306

# AWS S3 (para subida de imágenes en producción)
AWS_ACCESS_KEY_ID=tu_access_key_id
AWS_SECRET_ACCESS_KEY=tu_secret_access_key
AWS_REGION=us-east-2
AWS_S3_BUCKET=viva-inspectores-storage

# Configuración del servidor
PORT=3000
NODE_ENV=development

# Configuración de imágenes
MAX_IMAGE_SIZE=10485760
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/webp

# ====================================================
# INSTRUCCIONES:
# 1. Copiar este archivo: cp env.template .env
# 2. Completar con valores reales de tu entorno
# 3. Para desarrollo local, puedes dejar AWS vacío
# ====================================================