Project VIVA_INSPECTORES {
  database_type: 'MariaDB'
  Note: '''
  # Sistema VIVA_INSPECTORES - Base de Datos
  
  Sistema de digitalización de inspecciones aeronáuticas para Vivaaerobus.
  
  ## Características:
  - Gestión de inspectores certificados
  - Registro de aeronaves y motores
  - Formularios de inspección digitales
  - Evidencia fotográfica integrada
  - Generación de reportes oficiales
  - Auditoría completa de cambios
  
  ## Integración:
  - Se integra con sistema MRO externo
  - Solo maneja números de Work Orders (no gestión completa)
  - Enfoque en creación de reportes de inspección
  '''
}

// ================================================
// GESTIÓN DE INSPECTORES Y CERTIFICACIONES
// ================================================

Table inspectors [headercolor: #3498DB] {
  inspector_id char(36) [pk, note: 'UUID único del inspector']
  employee_number varchar(20) [unique, not null, note: 'Número de empleado corporativo']
  first_name varchar(50) [not null, note: 'Nombre(s) del inspector']
  last_name varchar(50) [not null, note: 'Apellido(s) del inspector']
  email varchar(100) [unique, not null, note: 'Email corporativo']
  license_number varchar(30) [unique, note: 'Número de licencia de inspector']
  hire_date date [not null, note: 'Fecha de contratación']
  is_active boolean [default: true, note: 'Inspector activo en el sistema']
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`]
  
  indexes {
    employee_number [name: 'idx_employee_number']
    email [name: 'idx_email']
    license_number [name: 'idx_license_number']
    (is_active, last_name, first_name) [name: 'idx_active_inspectors']
  }
  
  Note: 'Tabla principal de inspectores certificados de Vivaaerobus'
}

Table inspector_certifications [headercolor: #E74C3C] {
  certification_id char(36) [pk, note: 'UUID único de certificación']
  inspector_id char(36) [ref: > inspectors.inspector_id, not null]
  certification_type certification_type_enum [not null, note: 'Tipo de certificación específica']
  certification_number varchar(50) [not null, note: 'Número oficial de certificación']
  issued_date date [not null, note: 'Fecha de emisión']
  expiry_date date [not null, note: 'Fecha de vencimiento']
  issuing_authority varchar(100) [not null, note: 'Autoridad emisora']
  is_current boolean [default: true, note: 'Certificación vigente']
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`]
  
  indexes {
    (inspector_id, certification_type) [unique, name: 'uk_inspector_cert_type']
    certification_type [name: 'idx_cert_type']
    expiry_date [name: 'idx_cert_expiry']
    (is_current, expiry_date) [name: 'idx_current_certs']
  }
  
  Note: 'Certificaciones específicas por inspector (BSI, NDT, Ultrasonic, etc.)'
}

enum certification_type_enum {
  BSI [note: 'Borescope Inspection']
  NDT [note: 'Non-Destructive Testing']
  ULTRASONIC [note: 'Ultrasonic Testing']
  VISUAL [note: 'Visual Inspection']
  EDDY_CURRENT [note: 'Eddy Current Testing']
}

// ================================================
// GESTIÓN DE FLOTA Y COMPONENTES
// ================================================

Table aircraft [headercolor: #27AE60] {
  aircraft_id char(36) [pk, note: 'UUID único de aeronave']
  registration varchar(10) [unique, not null, note: 'Matrícula internacional (ej: XA-ABC)']
  manufacturer varchar(50) [not null, note: 'Fabricante (Airbus, Boeing, etc.)']
  model varchar(50) [not null, note: 'Modelo específico (A320, 737, etc.)']
  serial_number varchar(50) [unique, note: 'Número de serie del fabricante']
  year_manufactured year [not null, note: 'Año de fabricación']
  entry_service_date date [note: 'Fecha de entrada en servicio con Vivaaerobus']
  is_active_fleet boolean [default: true, note: 'Aeronave activa en la flota']
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`]
  
  indexes {
    registration [name: 'idx_registration']
    (manufacturer, model) [name: 'idx_manufacturer_model']
    (is_active_fleet, registration) [name: 'idx_active_fleet']
    serial_number [name: 'idx_serial_number']
  }
  
  Note: 'Registro completo de aeronaves en la flota de Vivaaerobus'
}

Table engines [headercolor: #F39C12] {
  engine_id char(36) [pk, note: 'UUID único del motor']
  aircraft_id char(36) [ref: > aircraft.aircraft_id, not null]
  engine_position engine_position_enum [not null, note: 'Posición del motor en la aeronave']
  manufacturer varchar(50) [not null, note: 'Fabricante del motor (P&W, CFM, etc.)']
  model varchar(50) [not null, note: 'Modelo específico (PW1100, V2500, etc.)']
  serial_number varchar(50) [unique, not null, note: 'Número de serie único del motor']
  total_cycles int [default: 0, note: 'Ciclos totales de operación']
  total_flight_hours decimal(10,2) [default: 0.00, note: 'Horas de vuelo totales']
  installation_date date [note: 'Fecha de instalación en aeronave']
  last_overhaul_date date [note: 'Fecha del último overhaul mayor']
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`]
  
  indexes {
    (aircraft_id, engine_position) [unique, name: 'uk_aircraft_position']
    serial_number [name: 'idx_engine_serial']
    (manufacturer, model) [name: 'idx_engine_manufacturer_model']
    (total_cycles, total_flight_hours) [name: 'idx_cycles_hours']
  }
  
  Note: 'Motores específicos instalados en cada aeronave'
}

enum engine_position_enum {
  LEFT [note: 'Motor izquierdo']
  RIGHT [note: 'Motor derecho']
  CENTER [note: 'Motor central']
  ENGINE_1 [note: 'Motor 1 (quad-engine)']
  ENGINE_2 [note: 'Motor 2 (quad-engine)']
  ENGINE_3 [note: 'Motor 3 (quad-engine)']
  ENGINE_4 [note: 'Motor 4 (quad-engine)']
}

// ================================================
// NÚCLEO DE INSPECCIONES
// ================================================

Table inspections [headercolor: #9B59B6] {
  inspection_id char(36) [pk, note: 'UUID único de inspección']
  work_order_number varchar(20) [not null, note: 'Número de WO del sistema MRO externo']
  aircraft_id char(36) [ref: > aircraft.aircraft_id, not null]
  inspector_id char(36) [ref: > inspectors.inspector_id, not null]
  inspection_type inspection_type_enum [not null, note: 'Tipo específico de inspección']
  engine_id char(36) [ref: > engines.engine_id, note: 'Motor específico (NULL para inspecciones de aeronave)']
  station_code varchar(5) [not null, note: 'Código de estación donde se realizó']
  form_data json [not null, note: 'Datos específicos del formulario de inspección']
  validation_data json [note: 'Resultados de validaciones automáticas']
  status inspection_status_enum [default: 'DRAFT', note: 'Estado actual de la inspección']
  findings_summary text [note: 'Resumen de hallazgos principales']
  recommendations text [note: 'Recomendaciones técnicas']
  started_at datetime [note: 'Inicio real de la inspección']
  completed_at datetime [note: 'Finalización de la inspección']
  approved_at datetime [note: 'Fecha de aprobación final']
  approved_by char(36) [ref: > inspectors.inspector_id, note: 'Inspector que aprobó']
  created_at timestamp [default: `CURRENT_TIMESTAMP`]
  updated_at timestamp [default: `CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`]
  
  indexes {
    work_order_number [name: 'idx_work_order_number']
    aircraft_id [name: 'idx_aircraft']
    inspector_id [name: 'idx_inspector']
    inspection_type [name: 'idx_inspection_type']
    (status, completed_at) [name: 'idx_status_date']
    engine_id [name: 'idx_engine']
    station_code [name: 'idx_station_code']
  }
  
  Note: 'Tabla principal de inspecciones realizadas. Integrada con sistema MRO externo.'
}

enum inspection_type_enum {
  BSI_PW1100 [note: 'Borescope Inspection para motores PW1100']
  BSI_V2500 [note: 'Borescope Inspection para motores V2500']
  NDT_003 [note: 'Non-Destructive Testing Rev 3']
  ULTRASONIC [note: 'Inspección ultrasónica']
  SPOT_CHECK [note: 'Inspección puntual']
  EDDY_CURRENT [note: 'Inspección por corrientes de Eddy']
}

enum inspection_status_enum {
  DRAFT [note: 'Borrador inicial']
  IN_PROGRESS [note: 'En proceso de llenado']
  COMPLETED [note: 'Inspección completada']
  APPROVED [note: 'Aprobada por supervisor']
  REJECTED [note: 'Rechazada, requiere corrección']
  CANCELLED [note: 'Cancelada']
}

// ================================================
// EVIDENCIA Y DOCUMENTACIÓN
// ================================================

Table inspection_evidence [headercolor: #1ABC9C] {
  evidence_id char(36) [pk, note: 'UUID único de evidencia']
  inspection_id char(36) [ref: > inspections.inspection_id, not null]
  file_name varchar(255) [not null, note: 'Nombre original del archivo']
  file_path text [not null, note: 'Ruta completa del archivo en storage']
  file_size_bytes bigint [not null, note: 'Tamaño del archivo en bytes']
  mime_type varchar(100) [not null, note: 'Tipo MIME (image/jpeg, image/png, etc.)']
  image_width int [note: 'Ancho de imagen en píxeles']
  image_height int [note: 'Alto de imagen en píxeles']
  description text [note: 'Descripción técnica de la evidencia']
  sequence_order int [default: 1, note: 'Orden secuencial en la inspección']
  uploaded_at timestamp [default: `CURRENT_TIMESTAMP`]
  uploaded_by char(36) [ref: > inspectors.inspector_id, not null]
  
  indexes {
    inspection_id [name: 'idx_inspection_evidence']
    uploaded_by [name: 'idx_uploaded_by']
    (inspection_id, sequence_order) [name: 'idx_evidence_sequence']
    mime_type [name: 'idx_mime_type']
  }
  
  Note: 'Evidencia fotográfica asociada a cada inspección'
}

Table official_reports [headercolor: #E67E22] {
  report_id char(36) [pk, note: 'UUID único del reporte']
  inspection_id char(36) [ref: - inspections.inspection_id, not null]
  document_type document_type_enum [not null, note: 'Tipo de documento generado']
  file_path text [not null, note: 'Ruta del documento generado']
  file_size_bytes bigint [not null, note: 'Tamaño del archivo en bytes']
  checksum_md5 varchar(32) [not null, note: 'Hash MD5 para verificación de integridad']
  generated_at timestamp [default: `CURRENT_TIMESTAMP`]
  generated_by char(36) [ref: > inspectors.inspector_id, not null]
  template_version varchar(10) [not null, note: 'Versión de plantilla utilizada']
  is_official_copy boolean [default: true, note: 'Copia oficial para autoridades']
  
  indexes {
    inspection_id [unique, name: 'uk_inspection_report']
    generated_by [name: 'idx_generated_by']
    document_type [name: 'idx_document_type']
    generated_at [name: 'idx_generated_date']
    checksum_md5 [name: 'idx_checksum']
  }
  
  Note: 'Documentos oficiales generados para cada inspección completada'
}

enum document_type_enum {
  PDF [note: 'Documento en formato PDF']
  DOCX [note: 'Documento en formato Word']
  BOTH [note: 'Ambos formatos generados']
}

// ================================================
// AUDITORÍA Y TRAZABILIDAD
// ================================================

Table audit_log [headercolor: #34495E] {
  audit_id char(36) [pk, note: 'UUID único del registro de auditoría']
  table_name varchar(50) [not null, note: 'Nombre de tabla afectada']
  record_id varchar(50) [not null, note: 'ID del registro modificado']
  operation_type operation_type_enum [not null, note: 'Tipo de operación realizada']
  old_values json [note: 'Valores anteriores (para UPDATE/DELETE)']
  new_values json [note: 'Valores nuevos (para INSERT/UPDATE)']
  changed_by char(36) [ref: > inspectors.inspector_id, not null]
  changed_at timestamp [default: `CURRENT_TIMESTAMP`]
  ip_address varchar(45) [note: 'Dirección IP del usuario']
  user_agent text [note: 'User Agent del navegador']
  session_id varchar(100) [note: 'ID de sesión del usuario']
  
  indexes {
    (table_name, record_id) [name: 'idx_table_record']
    changed_by [name: 'idx_changed_by']
    changed_at [name: 'idx_changed_at']
    operation_type [name: 'idx_operation_type']
    (table_name, changed_at) [name: 'idx_table_date']
  }
  
  Note: 'Registro completo de auditoría para trazabilidad de cambios'
}

enum operation_type_enum {
  INSERT [note: 'Inserción de nuevo registro']
  UPDATE [note: 'Actualización de registro existente']
  DELETE [note: 'Eliminación de registro']
}

// ================================================
// NOTAS GENERALES
// ================================================

Note general_notes {
'''
## Consideraciones de Diseño

### Integración con Sistema MRO
- El sistema NO gestiona órdenes de trabajo completas
- Solo almacena work_order_number como referencia al sistema MRO externo
- Enfoque exclusivo en creación y gestión de reportes de inspección

### Seguridad y Auditoría
- Todos los cambios son registrados en audit_log
- Integridad referencial estricta con foreign keys
- Timestamps automáticos para trazabilidad

### Performance
- Índices optimizados para consultas frecuentes
- UUIDs para escalabilidad y seguridad
- Campos JSON para flexibilidad en formularios

### Cumplimiento Regulatorio
- Estructura diseñada para industria aeronáutica
- Trazabilidad completa requerida por autoridades
- Certificaciones de inspectores validadas por el sistema
'''
} 