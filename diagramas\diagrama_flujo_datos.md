# Diagrama de Flujo de Datos - Sistema VIVA_INSPECTORES

```mermaid
sequenceDiagram
    participant U as Usuario
    participant F as Form Handler
    participant V as Validator
    participant G as Generator
    participant S as Servidor
    participant FS as File System
    
    U->>F: Llenar formulario
    F->>V: <PERSON><PERSON><PERSON> datos
    V-->>F: Validación OK
    F->>S: <PERSON><PERSON><PERSON> datos + imágenes
    S->>G: Procesar formulario
    G->>FS: Leer plantilla de documento
    FS-->>G: Plantilla cargada
    G->>G: Reemplazar marcadores
    G->>S: Documento generado
    S-->>U: Descargar documento oficial
```

## Descripción
Este diagrama de secuencia muestra el flujo paso a paso de cómo interactúan los componentes cuando un usuario completa una inspección, desde el llenado del formulario hasta la descarga del documento oficial. 